/**
 * AWS Lambda Web Proxy for CloudFront
 * Node.js 22.x Runtime
 * 
 * This function fetches content from CloudFront and adds authentication cookies.
 * It handles both text and binary content, manages compression, and provides
 * secure cookie management for authenticated sessions.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import https from 'https';
import { URL } from 'url';

// Configuration constants
const CONFIG = {
  CLOUDFRONT_URL: 'https://d2h85rpj1ghd7n.cloudfront.net',
  REQUEST_TIMEOUT: 30000, // 30 seconds
  MAX_RETRIES: 3,
  COOKIE_OPTIONS: {
    httpOnly: true,
    secure: true,
    sameSite: 'Strict',
    path: '/'
  }
};

// Environment validation
const requiredEnvVars = [];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

/**
 * Main Lambda handler function
 * @param {Object} event - API Gateway Lambda Proxy integration event object
 * @param {Object} context - Lambda Context runtime methods and attributes
 * @returns {Object} API Gateway Lambda Proxy integration response object
 */
export const handler = async (event, context) => {
  // Set Lambda context options for better performance
  context.callbackWaitsForEmptyEventLoop = false;

  const requestId = context.awsRequestId;
  const startTime = Date.now();

  try {
    // Log request start with minimal event data for security
    console.log(`[${requestId}] Web proxy request started`, {
      httpMethod: event.httpMethod,
      path: event.path,
      pathParameters: event.pathParameters,
      userAgent: event.headers?.['user-agent'],
      requestId
    });

    // Extract path from the event
    const path = event.pathParameters?.proxy || '';
    const queryString = event.queryStringParameters ? 
      new URLSearchParams(event.queryStringParameters).toString() : '';
    
    // Construct the CloudFront URL
    let targetUrl = `${CONFIG.CLOUDFRONT_URL}/${path}`;
    if (queryString) {
      targetUrl += `?${queryString}`;
    }
    
    console.log(`[${requestId}] Fetching from CloudFront: ${targetUrl}`);
    
    // Extract and filter headers from the original request
    const forwardHeaders = extractForwardHeaders(event.headers);
    console.log(`[${requestId}] Forward headers:`, Object.keys(forwardHeaders));
    
    // Get user info from authorizer context
    const userId = extractUserId(event);
    console.log(`[${requestId}] User ID: ${userId}`);
    
    // Fetch content from CloudFront
    const response = await fetchFromCloudFront(targetUrl, event.httpMethod, event.body, forwardHeaders, requestId);
    
    // Generate authentication cookies
    const cookies = generateAuthCookies(userId);
    
    // Add cookies to existing Set-Cookie headers
    const existingCookies = response.headers['set-cookie'] || [];
    const allCookies = [...existingCookies, ...cookies];
    
    // Create prefixed request headers to avoid conflicts
    const requestHeaders = createPrefixedHeaders(forwardHeaders);
    
    const duration = Date.now() - startTime;
    console.log(`[${requestId}] Request completed successfully in ${duration}ms`);
    
    return {
      statusCode: response.statusCode,
      headers: {
        ...response.headers,
        ...requestHeaders,  // Include original request headers with prefix
        'Set-Cookie': allCookies.join(', '),
        'X-Request-ID': requestId,
        'X-Processing-Time': `${duration}ms`
      },
      body: response.body,
      isBase64Encoded: response.isBase64Encoded
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[${requestId}] Web proxy error after ${duration}ms:`, {
      error: error.message,
      stack: error.stack,
      requestId
    });
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
        'X-Processing-Time': `${duration}ms`
      },
      body: JSON.stringify({
        success: false,
        error: {
          message: 'Web proxy error',
          requestId,
          timestamp: new Date().toISOString()
        }
      })
    };
  }
};

/**
 * Extract and filter headers for forwarding to CloudFront
 * @param {Object} headers - Original request headers
 * @returns {Object} Filtered headers safe for forwarding
 */
function extractForwardHeaders(headers) {
  const forwardHeaders = {};
  
  if (!headers) {
    return forwardHeaders;
  }

  // Headers to exclude from forwarding
  const excludeHeaders = [
    'host',
    'blink-auth-proxysecret',
    'blink-auth-userid',
    'authorization',
    'x-forwarded-for',
    'x-forwarded-proto',
    'x-forwarded-port',
    'x-amzn-trace-id'
  ];

  Object.keys(headers).forEach(key => {
    const lowerKey = key.toLowerCase();
    if (!excludeHeaders.includes(lowerKey)) {
      forwardHeaders[key] = headers[key];
    }
  });

  return forwardHeaders;
}

/**
 * Extract user ID from event context
 * @param {Object} event - API Gateway event
 * @returns {string} User ID or 'anonymous'
 */
function extractUserId(event) {
  try {
    return event.requestContext?.authorizer?.principalId || 
           event.headers?.['blink-auth-userid'] || 
           'anonymous';
  } catch (error) {
    console.warn('Failed to extract user ID:', error.message);
    return 'anonymous';
  }
}

/**
 * Generate authentication cookies
 * @param {string} userId - User identifier
 * @returns {Array<string>} Array of cookie strings
 */
function generateAuthCookies(userId) {
  const sessionId = generateSessionId();
  const timestamp = Date.now();
  
  return [
    `_BAT=${userId}; Path=${CONFIG.COOKIE_OPTIONS.path}; HttpOnly; Secure; SameSite=${CONFIG.COOKIE_OPTIONS.sameSite}`,
    `session_id=${sessionId}; Path=${CONFIG.COOKIE_OPTIONS.path}; HttpOnly; Secure; SameSite=${CONFIG.COOKIE_OPTIONS.sameSite}`,
    `auth_timestamp=${timestamp}; Path=${CONFIG.COOKIE_OPTIONS.path}; HttpOnly; Secure; SameSite=${CONFIG.COOKIE_OPTIONS.sameSite}`
  ];
}

/**
 * Create prefixed headers to avoid conflicts with CloudFront response headers
 * @param {Object} forwardHeaders - Headers to prefix
 * @returns {Object} Prefixed headers
 */
function createPrefixedHeaders(forwardHeaders) {
  const requestHeaders = {};
  
  Object.keys(forwardHeaders).forEach(key => {
    requestHeaders[`x-original-${key.toLowerCase()}`] = forwardHeaders[key];
  });
  
  return requestHeaders;
}

/**
 * Generate a unique session ID
 * @returns {string} Session ID
 */
function generateSessionId() {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15) +
         Date.now().toString(36);
}

/**
 * Fetch content from CloudFront with retry logic
 * @param {string} url - Target URL
 * @param {string} method - HTTP method
 * @param {string|null} body - Request body
 * @param {Object} headers - Request headers
 * @param {string} requestId - Request ID for logging
 * @returns {Promise<Object>} Response object
 */
async function fetchFromCloudFront(url, method = 'GET', body = null, headers = {}, requestId) {
  let lastError;

  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      console.log(`[${requestId}] CloudFront request attempt ${attempt}/${CONFIG.MAX_RETRIES}`);

      const response = await makeHttpRequest(url, method, body, headers, requestId);

      console.log(`[${requestId}] CloudFront response: ${response.statusCode}`);
      return response;

    } catch (error) {
      lastError = error;
      console.warn(`[${requestId}] CloudFront request attempt ${attempt} failed:`, error.message);

      if (attempt < CONFIG.MAX_RETRIES) {
        const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
        console.log(`[${requestId}] Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw new Error(`CloudFront request failed after ${CONFIG.MAX_RETRIES} attempts: ${lastError.message}`);
}

/**
 * Make HTTP request to CloudFront
 * @param {string} url - Target URL
 * @param {string} method - HTTP method
 * @param {string|null} body - Request body
 * @param {Object} headers - Request headers
 * @param {string} requestId - Request ID for logging
 * @returns {Promise<Object>} Response object
 */
function makeHttpRequest(url, method, body, headers, requestId) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: method,
      timeout: CONFIG.REQUEST_TIMEOUT,
      headers: {
        ...headers,
        'User-Agent': 'AWS-Lambda-Web-Proxy/1.0',
        // Remove accept-encoding to prevent compression issues
        'accept-encoding': 'identity'
      }
    };

    // Remove any compression-related headers from forwarded headers
    delete options.headers['accept-encoding'];
    delete options.headers['Accept-Encoding'];

    console.log(`[${requestId}] Making ${method} request to ${urlObj.hostname}${urlObj.pathname}`);

    const req = https.request(options, (res) => {
      let data = '';
      let chunks = [];

      // Determine if response is binary
      const contentType = res.headers['content-type'] || '';
      const isBinary = !contentType.startsWith('text/') &&
                       !contentType.includes('application/json') &&
                       !contentType.includes('application/javascript') &&
                       !contentType.includes('application/xml') &&
                       !contentType.includes('application/css');

      if (isBinary) {
        res.on('data', (chunk) => {
          chunks.push(chunk);
        });
      } else {
        res.setEncoding('utf8');
        res.on('data', (chunk) => {
          data += chunk;
        });
      }

      res.on('end', () => {
        const responseHeaders = {};
        Object.keys(res.headers).forEach(key => {
          // Filter out compression-related headers
          if (key.toLowerCase() !== 'transfer-encoding' &&
              key.toLowerCase() !== 'connection' &&
              key.toLowerCase() !== 'content-encoding') {
            responseHeaders[key] = res.headers[key];
          }
        });

        resolve({
          statusCode: res.statusCode,
          headers: responseHeaders,
          body: isBinary ? Buffer.concat(chunks).toString('base64') : data,
          isBase64Encoded: isBinary
        });
      });
    });

    req.on('error', (error) => {
      console.error(`[${requestId}] HTTP request error:`, error.message);
      reject(error);
    });

    req.on('timeout', () => {
      console.error(`[${requestId}] HTTP request timeout after ${CONFIG.REQUEST_TIMEOUT}ms`);
      req.destroy();
      reject(new Error(`Request timeout after ${CONFIG.REQUEST_TIMEOUT}ms`));
    });

    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      req.write(body);
    }

    req.end();
  });
}
