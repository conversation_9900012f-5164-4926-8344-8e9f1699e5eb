/**
 * AWS Lambda Web Proxy for CloudFront
 * Node.js 22.x Runtime
 * 
 * This function fetches content from CloudFront and adds authentication cookies
 */

import https from 'https';
import { URL } from 'url';

const CLOUDFRONT_URL = 'https://d2h85rpj1ghd7n.cloudfront.net';

export const handler = async (event, context) => {
    try {
        console.log('Event:', JSON.stringify(event, null, 2));
        
        // Extract path from the event
        const path = event.pathParameters?.proxy || '';
        const queryString = event.queryStringParameters ? 
            new URLSearchParams(event.queryStringParameters).toString() : '';
        
        // Construct the CloudFront URL
        let targetUrl = `${CLOUDFRONT_URL}/${path}`;
        if (queryString) {
            targetUrl += `?${queryString}`;
        }
        
        console.log(`Fetching from CloudFront: ${targetUrl}`);
        
        // Extract headers from the original request (excluding host)
        const forwardHeaders = {};
        if (event.headers) {
            Object.keys(event.headers).forEach(key => {
                if (key.toLowerCase() !== 'host' && 
                    key.toLowerCase() !== 'blink-auth-proxysecret' &&
                    key.toLowerCase() !== 'blink-auth-userid') {
                    forwardHeaders[key] = event.headers[key];
                }
            });
        }
        
        // Get user info from authorizer context
        const userId = event.requestContext?.authorizer?.booleanKey ? 
            event.headers['blink-auth-userid'] : 'anonymous';
        
        // Fetch content from CloudFront
        const response = await fetchFromCloudFront(targetUrl, event.httpMethod, event.body, forwardHeaders);
        
        // Prepare cookies to set
        const cookies = [
            `_BAT=${userId}; Path=/; HttpOnly; Secure; SameSite=Strict`,
            `session_id=${generateSessionId()}; Path=/; HttpOnly; Secure; SameSite=Strict`
        ];
        
        // Add cookies to existing Set-Cookie headers
        const existingCookies = response.headers['set-cookie'] || [];
        const allCookies = [...existingCookies, ...cookies];
        
        return {
            statusCode: response.statusCode,
            headers: {
                ...response.headers,
                'Set-Cookie': allCookies.join(', ')
            },
            body: response.body,
            isBase64Encoded: response.isBase64Encoded
        };
        
    } catch (error) {
        console.error('Web proxy error:', error);
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: 'Internal server error',
                error: error.message
            })
        };
    }
};

async function fetchFromCloudFront(url, method = 'GET', body = null, headers = {}) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        
        const options = {
            hostname: urlObj.hostname,
            port: urlObj.port || 443,
            path: urlObj.pathname + urlObj.search,
            method: method,
            headers: {
                ...headers,
                'User-Agent': 'AWS-Lambda-Web-Proxy/1.0'
            }
        };
        
        const req = https.request(options, (res) => {
            let data = '';
            let chunks = [];
            
            // Determine if response is binary
            const contentType = res.headers['content-type'] || '';
            const isBinary = !contentType.startsWith('text/') && 
                           !contentType.includes('application/json') &&
                           !contentType.includes('application/javascript') &&
                           !contentType.includes('application/xml');
            
            if (isBinary) {
                res.on('data', (chunk) => {
                    chunks.push(chunk);
                });
            } else {
                res.setEncoding('utf8');
                res.on('data', (chunk) => {
                    data += chunk;
                });
            }
            
            res.on('end', () => {
                const responseHeaders = {};
                Object.keys(res.headers).forEach(key => {
                    if (key.toLowerCase() !== 'transfer-encoding' && 
                        key.toLowerCase() !== 'connection') {
                        responseHeaders[key] = res.headers[key];
                    }
                });
                
                resolve({
                    statusCode: res.statusCode,
                    headers: responseHeaders,
                    body: isBinary ? Buffer.concat(chunks).toString('base64') : data,
                    isBase64Encoded: isBinary
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
            req.write(body);
        }
        
        req.end();
    });
}

function generateSessionId() {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15) + 
           Date.now().toString(36);
}
