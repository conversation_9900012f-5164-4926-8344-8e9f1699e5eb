/**
 * AWS Lambda Authorizer for API Gateway
 * Node.js 22.x Runtime
 * 
 * This function validates incoming requests by checking for required headers
 * and comparing the proxy secret against an environment variable.
 */

export const handler = async (event, context, callback) => {
    try {
        const denyResponse = {
            principalId: 'user',
            policyDocument: {
                Version: '2012-10-17',
                Statement: [
                    {
                        Action: 'execute-api:Invoke',
                        Effect: 'Deny',
                        Resource: event.methodArn,
                    },
                ],
            },
        };

        // Log all event properties for debugging
        for (const [key, value] of Object.entries(event)) {
            console.log(`${key}: ${JSON.stringify(value)}`);
        }

        const headers = event.headers;
        
        if (!headers) {
            console.log("No headers present");
            return denyResponse;
        }

        const requestProxySecret = headers['blink-auth-proxysecret'];
        const requestUserId = headers['blink-auth-userid'];

        if (!requestProxySecret) {
            console.log("No blink-auth-proxysecret header present");
            return denyResponse;
        }

        if (!requestUserId) {
            console.log("No blink-auth-userid header present");
            return denyResponse;
        }

        console.log(`Request Blink proxy secret: ${requestProxySecret}`);
        console.log(`Request Blink user ID: ${requestUserId}`);

        /*
         * Validate the incoming token and produce the principal user identifier
         * associated with the token. This can be accomplished in a number of ways:
         *
         * 1. Call out to the OAuth provider
         * 2. Decode a JWT token inline
         * 3. Lookup in a self-managed DB
         */

        const blinkProxySecret = process.env.BlinkProxySecretKey;
        console.log("Blink proxy secret saved in lambda: " + blinkProxySecret);

        if (blinkProxySecret !== requestProxySecret) {
            console.log('Wrong secret used');
            return denyResponse;
        }

        console.log('Allowed');

        return {
            principalId: requestUserId,
            policyDocument: {
                Version: '2012-10-17',
                Statement: [
                    {
                        Action: 'execute-api:Invoke',
                        Effect: 'Allow',
                        Resource: event.methodArn,
                    },
                ],
            },
        };
        

    } catch (error) {
        console.error('Authorization failed:', error.message);
        // For Lambda authorizers, throwing an error results in a 401 Unauthorized
        throw error;
    }
};
