/**
 * AWS Lambda Authorizer for API Gateway
 * Node.js 22.x Runtime
 *
 * This function validates incoming requests by checking for required headers
 * and comparing the proxy secret against an environment variable.
 *
 * Features:
 * - Header validation for blink-auth-proxysecret and blink-auth-userid
 * - Environment-based secret validation
 * - Comprehensive logging for debugging
 * - Structured error handling
 * - No caching for real-time authorization
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

// Configuration constants
const CONFIG = {
  REQUIRED_HEADERS: {
    PROXY_SECRET: 'blink-auth-proxysecret',
    USER_ID: 'blink-auth-userid'
  },
  ENV_VARS: {
    PROXY_SECRET: 'BlinkProxySecretKey'
  }
};

// Environment validation
const requiredEnvVars = [CONFIG.ENV_VARS.PROXY_SECRET];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

/**
 * Main Lambda authorizer handler
 * @param {Object} event - API Gateway authorizer event
 * @param {Object} context - Lambda context
 * @returns {Object} Authorization response
 */
export const handler = async (event, context) => {
  // Set Lambda context options for better performance
  context.callbackWaitsForEmptyEventLoop = false;

  const requestId = context.awsRequestId;
  const startTime = Date.now();

  try {
    // Log request start with minimal information for security
    console.log(`[${requestId}] Authorization request started`, {
      methodArn: event.methodArn,
      httpMethod: event.httpMethod,
      path: event.path,
      requestId
    });

    // Validate event structure
    const validationResult = validateAuthorizerEvent(event);
    if (!validationResult.isValid) {
      console.warn(`[${requestId}] Event validation failed:`, validationResult.errors);
      return createDenyResponse('user', event.methodArn);
    }

    // Extract and validate headers
    const headers = event.headers || {};
    const requestProxySecret = headers[CONFIG.REQUIRED_HEADERS.PROXY_SECRET];
    const requestUserId = headers[CONFIG.REQUIRED_HEADERS.USER_ID];

    // Validate required headers presence
    if (!requestProxySecret) {
      console.warn(`[${requestId}] Missing ${CONFIG.REQUIRED_HEADERS.PROXY_SECRET} header`);
      return createDenyResponse('user', event.methodArn);
    }

    if (!requestUserId) {
      console.warn(`[${requestId}] Missing ${CONFIG.REQUIRED_HEADERS.USER_ID} header`);
      return createDenyResponse('user', event.methodArn);
    }

    // Validate header values
    if (!isValidUserId(requestUserId)) {
      console.warn(`[${requestId}] Invalid user ID format`);
      return createDenyResponse('user', event.methodArn);
    }

    console.log(`[${requestId}] Validating credentials for user: ${requestUserId}`);

    // Get environment secret
    const blinkProxySecret = process.env[CONFIG.ENV_VARS.PROXY_SECRET];

    if (!blinkProxySecret) {
      console.error(`[${requestId}] Environment variable ${CONFIG.ENV_VARS.PROXY_SECRET} not found`);
      return createDenyResponse('user', event.methodArn);
    }

    // Validate proxy secret
    if (!isValidSecret(blinkProxySecret, requestProxySecret)) {
      console.warn(`[${requestId}] Invalid proxy secret provided`);
      return createDenyResponse('user', event.methodArn);
    }

    const duration = Date.now() - startTime;
    console.log(`[${requestId}] Authorization successful for user ${requestUserId} in ${duration}ms`);

    // Return allow response
    return createAllowResponse(requestUserId, event.methodArn);

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[${requestId}] Authorization failed after ${duration}ms:`, {
      error: error.message,
      stack: error.stack,
      requestId
    });

    // For Lambda authorizers, throwing an error results in a 401 Unauthorized
    throw new Error('Authorization failed');
  }
};

/**
 * Validate the authorizer event structure
 * @param {Object} event - API Gateway authorizer event
 * @returns {Object} Validation result
 */
function validateAuthorizerEvent(event) {
  const errors = [];

  if (!event) {
    errors.push('Event object is null or undefined');
    return { isValid: false, errors };
  }

  // Check required properties
  const requiredProperties = ['methodArn', 'headers'];
  requiredProperties.forEach(prop => {
    if (!(prop in event)) {
      errors.push(`Missing required property: ${prop}`);
    }
  });

  // Validate methodArn format
  if (event.methodArn && typeof event.methodArn !== 'string') {
    errors.push('methodArn must be a string');
  }

  // Validate headers
  if (event.headers !== null && typeof event.headers !== 'object') {
    errors.push('Headers must be an object or null');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate user ID format
 * @param {string} userId - User ID to validate
 * @returns {boolean} Whether the user ID is valid
 */
function isValidUserId(userId) {
  if (!userId || typeof userId !== 'string') {
    return false;
  }

  // Basic validation - adjust regex as needed for your user ID format
  const userIdPattern = /^[a-zA-Z0-9._-]+$/;
  return userIdPattern.test(userId.trim()) && userId.trim().length > 0 && userId.trim().length <= 100;
}

/**
 * Validate proxy secret using secure comparison
 * @param {string} expectedSecret - Expected secret from environment
 * @param {string} providedSecret - Secret provided in request
 * @returns {boolean} Whether the secrets match
 */
function isValidSecret(expectedSecret, providedSecret) {
  if (!expectedSecret || !providedSecret) {
    return false;
  }

  if (typeof expectedSecret !== 'string' || typeof providedSecret !== 'string') {
    return false;
  }

  // Use timing-safe comparison to prevent timing attacks
  return timingSafeEqual(expectedSecret, providedSecret);
}

/**
 * Timing-safe string comparison to prevent timing attacks
 * @param {string} a - First string
 * @param {string} b - Second string
 * @returns {boolean} Whether strings are equal
 */
function timingSafeEqual(a, b) {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

/**
 * Create a deny authorization response
 * @param {string} principalId - Principal ID for the response
 * @param {string} methodArn - Method ARN from the event
 * @returns {Object} Deny authorization response
 */
function createDenyResponse(principalId, methodArn) {
  return {
    principalId: principalId || 'user',
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: 'Deny',
          Resource: methodArn || '*'
        }
      ]
    }
  };
}

/**
 * Create an allow authorization response
 * @param {string} principalId - Principal ID (user ID)
 * @param {string} methodArn - Method ARN from the event
 * @returns {Object} Allow authorization response
 */
function createAllowResponse(principalId, methodArn) {
  return {
    principalId: principalId,
    policyDocument: {
      Version: '2012-10-17',
      Statement: [
        {
          Action: 'execute-api:Invoke',
          Effect: 'Allow',
          Resource: methodArn || '*'
        }
      ]
    },
    context: {
      userId: principalId,
      timestamp: new Date().toISOString()
    }
  };
}
