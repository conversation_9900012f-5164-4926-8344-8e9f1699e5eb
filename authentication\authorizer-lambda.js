/**
 * AWS Lambda Authorizer for API Gateway
 * Node.js 22.x Runtime
 * 
 * This function validates incoming requests by checking for required headers
 * and comparing the proxy secret against an environment variable.
 */

export const handler = async (event, context) => {
    try {
        // Log all event properties for debugging
        for (const [key, value] of Object.entries(event)) {
            console.log(`${key}: ${JSON.stringify(value)}`);
        }

        const headers = event.headers;
        
        if (!headers) {
            console.log("No headers present");
            throw new Error('Unauthorized');
        }

        const requestProxySecret = headers['blink-auth-proxysecret'];
        const requestUserId = headers['blink-auth-userid'];

        if (!requestProxySecret) {
            console.log("No blink-auth-proxysecret header present");
            throw new Error('Unauthorized');
        }

        if (!requestUserId) {
            console.log("No blink-auth-userid header present");
            throw new Error('Unauthorized');
        }

        console.log(`Request Blink proxy secret: ${requestProxySecret}`);
        console.log(`Request Blink user ID: ${requestUserId}`);

        /*
         * Validate the incoming token and produce the principal user identifier
         * associated with the token. This can be accomplished in a number of ways:
         *
         * 1. Call out to the OAuth provider
         * 2. Decode a JWT token inline
         * 3. Lookup in a self-managed DB
         */

        const blinkProxySecret = process.env.BlinkProxySecretKey;
        console.log("Blink proxy secret saved in lambda: " + blinkProxySecret);

        if (blinkProxySecret !== requestProxySecret) {
            console.log('Wrong secret used');
            throw new Error('Unauthorized');
        }

        console.log('Allowed');
        const response = {
            isAuthorized: true,
            context: {
                stringKey: "value",
                numberKey: 1,
                booleanKey: true,
                arrayKey: ["value1", "value2"],
                mapKey: { "value1": "value2" }
            }
        };
        
        return response;
        
    } catch (error) {
        console.error('Authorization failed:', error.message);
        // For Lambda authorizers, throwing an error results in a 401 Unauthorized
        throw error;
    }
};
