# BPA Authentication Module

AWS Lambda authorizer function for API Gateway custom authorization in the BPA defect reporting system.

## Overview

This module provides secure authentication for the BPA API Gateway using a custom Lambda authorizer. It validates incoming requests by checking required headers and comparing secrets against environment variables.

## Features

### 🔐 Security Features

- **Header Validation**: Validates presence and format of required authentication headers
- **Secret Validation**: Secure comparison of proxy secrets using timing-safe algorithms
- **Input Sanitization**: Validates and sanitizes all input parameters
- **Timing Attack Prevention**: Uses constant-time comparison for secret validation
- **Comprehensive Logging**: Structured logging with request correlation

### 📊 Authorization Flow

```
Client Request → API Gateway → Lambda Authorizer → Allow/Deny Response
                                      ↓
                              Environment Secret Validation
```

### 🚀 Performance Optimizations

- **No Caching**: Real-time authorization for immediate security updates
- **Fast Validation**: Efficient header and secret validation
- **Context Optimization**: Optimized Lambda context for better performance
- **Structured Responses**: Standardized IAM policy responses

## Configuration

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `BlinkProxySecretKey` | Secret key for proxy authentication | `your-secret-key-here` |

### Required Headers

| Header | Description | Format |
|--------|-------------|--------|
| `blink-auth-proxysecret` | Proxy authentication secret | String |
| `blink-auth-userid` | User identifier | Alphanumeric string (1-100 chars) |

## API

### Input Event

The authorizer receives an API Gateway authorizer event:

```javascript
{
  "type": "REQUEST",
  "methodArn": "arn:aws:execute-api:us-east-1:************:abcdef123/test/GET/request",
  "resource": "/request",
  "path": "/request",
  "httpMethod": "GET",
  "headers": {
    "blink-auth-proxysecret": "your-secret-key",
    "blink-auth-userid": "user123",
    "user-agent": "Mozilla/5.0...",
    // ... other headers
  },
  "queryStringParameters": {},
  "pathParameters": {},
  "requestContext": {
    "accountId": "************",
    "apiId": "abcdef123",
    "stage": "test",
    "requestId": "c6af9ac6-7b61-11e6-9a41-93e8deadbeef"
  }
}
```

### Output Response

#### Allow Response

```javascript
{
  "principalId": "user123",
  "policyDocument": {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Action": "execute-api:Invoke",
        "Effect": "Allow",
        "Resource": "arn:aws:execute-api:us-east-1:************:abcdef123/test/GET/request"
      }
    ]
  },
  "context": {
    "userId": "user123",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

#### Deny Response

```javascript
{
  "principalId": "user",
  "policyDocument": {
    "Version": "2012-10-17",
    "Statement": [
      {
        "Action": "execute-api:Invoke",
        "Effect": "Deny",
        "Resource": "arn:aws:execute-api:us-east-1:************:abcdef123/test/GET/request"
      }
    ]
  }
}
```

## Validation Rules

### User ID Validation

- **Type**: String
- **Length**: 1-100 characters
- **Pattern**: Alphanumeric characters, dots, underscores, and hyphens only
- **Regex**: `/^[a-zA-Z0-9._-]+$/`

### Secret Validation

- **Comparison**: Timing-safe comparison to prevent timing attacks
- **Source**: Environment variable `BlinkProxySecretKey`
- **Security**: Constant-time comparison algorithm

### Event Validation

- **Required Fields**: `methodArn`, `headers`
- **Header Types**: Object or null
- **Method ARN**: Valid string format

## Error Handling

### Authorization Failures

All authorization failures result in a deny response with appropriate logging:

1. **Missing Headers**: Required headers not present
2. **Invalid User ID**: User ID format validation failure
3. **Invalid Secret**: Secret comparison failure
4. **Missing Environment**: Environment variable not configured
5. **Event Validation**: Malformed event structure

### Error Logging

```
[request-id] Authorization request started { methodArn: '...', httpMethod: 'GET', ... }
[request-id] Missing blink-auth-proxysecret header
[request-id] Authorization failed after 50ms: { error: '...', requestId: '...' }
```

## Security Considerations

### Timing Attack Prevention

The authorizer uses a timing-safe comparison function to prevent timing attacks:

```javascript
function timingSafeEqual(a, b) {
  if (a.length !== b.length) {
    return false;
  }
  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }
  return result === 0;
}
```

### Secure Logging

- **No Secret Logging**: Secrets are never logged
- **Request Correlation**: Each request has a unique ID for tracking
- **Minimal Information**: Only necessary information is logged

### Input Validation

- **Header Sanitization**: All headers are validated before processing
- **Length Limits**: User IDs have maximum length restrictions
- **Pattern Matching**: User IDs must match allowed character patterns

## Monitoring

### CloudWatch Logs

All authorization attempts are logged with structured information:

- Request start and completion times
- Validation failures with reasons
- Performance metrics (processing time)
- Error details with stack traces

### Metrics

- **Authorization Success Rate**: Percentage of successful authorizations
- **Processing Time**: Time taken for authorization decisions
- **Error Rates**: Different types of authorization failures

### Alarms

Consider setting up CloudWatch alarms for:

- High authorization failure rates
- Unusual processing times
- Missing environment variables

## Development

### Local Testing

```bash
# No dependencies required (uses only Node.js built-ins)
node authorizer-lambda.js
```

### Testing Headers

```bash
# Test with valid headers
curl -X GET \
  "https://api-gateway-url/endpoint" \
  -H "blink-auth-proxysecret: your-secret-key" \
  -H "blink-auth-userid: test-user"

# Test with invalid headers (should return 401)
curl -X GET \
  "https://api-gateway-url/endpoint" \
  -H "blink-auth-proxysecret: wrong-secret" \
  -H "blink-auth-userid: test-user"
```

### Environment Setup

```bash
# Set environment variable for testing
export BlinkProxySecretKey="your-test-secret"
```

## Deployment

The authorizer is deployed via Terraform as part of the BPA infrastructure:

- **Function Name**: `bpa-authorizer-lambda-{environment}`
- **Runtime**: Node.js 22.x
- **Timeout**: 30 seconds
- **Memory**: 128 MB (default)

### Terraform Configuration

```hcl
resource "aws_api_gateway_authorizer" "lambda_authorizer" {
  name                             = "bpa-lambda-authorizer-${var.environment}"
  rest_api_id                     = aws_api_gateway_rest_api.api.id
  authorizer_uri                  = aws_lambda_function.authorizer.invoke_arn
  type                            = "REQUEST"
  authorizer_result_ttl_in_seconds = 0  # No caching
  identity_source                 = ""  # No identity sources
}
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check header names and secret values
2. **500 Internal Error**: Check environment variable configuration
3. **Timeout**: Check Lambda function logs for performance issues

### Debug Steps

1. Check CloudWatch logs for the authorizer function
2. Verify environment variables are set correctly
3. Test with known good header values
4. Check API Gateway authorizer configuration

## Contributing

1. Follow AWS Lambda security best practices
2. Maintain timing-safe comparisons for secrets
3. Add comprehensive logging for debugging
4. Validate all inputs thoroughly
5. Update tests for new validation rules

## Security Best Practices

1. **Never log secrets** or sensitive information
2. **Use timing-safe comparisons** for all secret validations
3. **Validate all inputs** before processing
4. **Set appropriate timeouts** to prevent hanging requests
5. **Monitor authorization patterns** for anomalies
