# BPA Defect Reporting API Handler

AWS Lambda function for handling user authentication and data retrieval in the BPA (Bus Performance Analytics) system.

## Overview

This Lambda function serves as the backend API handler for user authentication and data retrieval operations. It validates incoming requests and retrieves user information from the Blink API.

## Architecture

- **Runtime**: Node.js 22.x
- **Integration**: AWS API Gateway Lambda Proxy
- **Authentication**: Custom Lambda authorizer
- **External APIs**: Blink API for user management
- **Processing**: User data retrieval and validation

## Functionality

### User Data Retrieval
The Lambda function automatically retrieves user information from the Blink API based on the authenticated user ID provided by the Lambda authorizer.

**Process Flow:**
1. Receives API Gateway Lambda Proxy event
2. Validates event structure and format
3. Extracts user ID from authorizer context
4. Calls Blink API to retrieve user information
5. Returns formatted user data

**Response Format:**
```json
{
  "success": true,
  "data": {
    "companyName": "Example Company",
    "firstName": "<PERSON>",
    "secondName": "Doe",
    "displayName": "<PERSON> Doe",
    "employeeId": "EMP001",
    "organisationId": "ORG001",
    "email": "<EMAIL>",
    "userId": "user123"
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `BLINK_APPLICATION_API_TOKEN` | API token for Blink service | Yes |
| `NODE_ENV` | Environment (development/production) | No |

## Request Processing

### Input Validation
- **Event Structure**: Validates API Gateway Lambda Proxy event format
- **Required Fields**: Ensures all necessary event properties are present
- **Security**: Validates request context and headers

### Error Handling
The function implements comprehensive error handling with standardized response formats:

#### Success Response
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### Error Response
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "statusCode": 400,
    "timestamp": "2024-01-01T00:00:00.000Z",
    "details": "Additional error details (development only)"
  }
}
```

## Security Features

- **Input Validation**: All inputs are validated and sanitized
- **Authentication**: Requires valid user authentication through Lambda authorizer
- **Data Sanitization**: Sensitive data is filtered from responses
- **Request Logging**: Comprehensive logging with request IDs for tracing
- **Error Handling**: Production-safe error messages (no internal details exposed)

## Performance Optimizations

- **Connection Reuse**: HTTP client configured for connection pooling
- **Timeout Management**: Appropriate timeouts for external API calls
- **Context Optimization**: `callbackWaitsForEmptyEventLoop = false` for better performance
- **Efficient Processing**: Streamlined user data retrieval with minimal overhead
- **Request Correlation**: Unique request IDs for performance tracking

## Development

### Local Testing
```bash
# Install dependencies
npm install

# Run tests (when implemented)
npm test

# Package for deployment
npm run package
```

### Code Structure
```
├── index.js                 # Main Lambda handler
├── utils/
│   ├── Authenticator.js     # User authentication and Blink API integration
│   ├── ResponseHelper.js    # Response formatting utilities
│   └── EventValidator.js    # Input validation utilities
├── package.json             # Dependencies and metadata
└── README.md               # This file
```

### Main Components

#### `index.js` - Main Handler
- **Event Validation**: Validates incoming API Gateway events
- **User Retrieval**: Orchestrates user data retrieval process
- **Error Handling**: Comprehensive error handling with proper HTTP status codes
- **Logging**: Structured logging with request correlation
- **Performance Tracking**: Execution time monitoring

#### `utils/Authenticator.js` - Blink API Integration
- **User ID Extraction**: Extracts user ID from authorizer context
- **API Communication**: Handles HTTP requests to Blink API
- **Data Transformation**: Converts Blink API response to standardized format
- **Error Handling**: Specific error handling for API failures

#### `utils/ResponseHelper.js` - Response Formatting
- **Success Responses**: Standardized success response format
- **Error Responses**: Standardized error response format
- **CORS Headers**: Automatic CORS header management
- **Data Sanitization**: Removes sensitive data from responses

#### `utils/EventValidator.js` - Input Validation
- **Event Structure**: Validates API Gateway event format
- **Required Fields**: Ensures all necessary properties are present
- **Type Checking**: Validates data types and formats

## Usage

### API Gateway Integration
This Lambda function is designed to be invoked via API Gateway Lambda Proxy integration. It expects:

- **HTTP Method**: POST
- **Path**: `/defect-reporting`
- **Headers**:
  - `blink-auth-proxysecret`: Authentication secret
  - `blink-auth-userid`: User identifier
  - `Content-Type`: application/json

### Request Format
The function automatically processes any valid API Gateway Lambda Proxy event and returns user information:

```bash
curl -X POST \
  "https://your-api-gateway-url/defect-reporting" \
  -H "Content-Type: application/json" \
  -H "blink-auth-proxysecret: your-secret" \
  -H "blink-auth-userid: user123" \
  -d '{}'
```

### Response Examples

#### Successful Response
```json
{
  "statusCode": 200,
  "headers": {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*"
  },
  "body": {
    "success": true,
    "data": {
      "companyName": "Example Company",
      "firstName": "John",
      "secondName": "Doe",
      "displayName": "John Doe",
      "employeeId": "EMP001",
      "organisationId": "ORG001",
      "email": "<EMAIL>",
      "userId": "user123"
    },
    "timestamp": "2024-01-01T12:00:00.000Z"
  }
}
```

#### Error Response
```json
{
  "statusCode": 500,
  "headers": {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*"
  },
  "body": {
    "success": false,
    "error": {
      "message": "User retrieval failed",
      "statusCode": 500,
      "timestamp": "2024-01-01T12:00:00.000Z"
    }
  }
}
```

## Deployment

This function is deployed via Terraform as part of the BPA infrastructure. See the main infrastructure documentation for deployment instructions.

## Monitoring

- **CloudWatch Logs**: All requests are logged with unique request IDs
- **Metrics**: Standard Lambda metrics available in CloudWatch
- **Tracing**: Request correlation through request IDs
