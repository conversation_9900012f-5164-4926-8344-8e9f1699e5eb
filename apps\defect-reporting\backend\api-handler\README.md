# BPA Defect Reporting API Handler

AWS Lambda function for handling defect reporting operations in the BPA (Bus Performance Analytics) system.

## Overview

This Lambda function serves as the backend API handler for defect reporting operations, including user authentication, defect card management, and image handling through S3 presigned URLs.

## Architecture

- **Runtime**: Node.js 22.x
- **Integration**: AWS API Gateway Lambda Proxy
- **Authentication**: Custom Lambda authorizer
- **External APIs**: Blink API for user management
- **Storage**: DynamoDB for defect cards, S3 for images

## Supported Actions

### 1. GetUser
Retrieves user information from Blink API based on the authenticated user ID.

**Request:**
```json
{
  "action": "GetUser"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "companyName": "Example Company",
    "firstName": "John",
    "secondName": "Doe",
    "displayName": "John Doe",
    "employeeId": "EMP001",
    "organisationId": "ORG001",
    "email": "<EMAIL>",
    "userId": "user123"
  }
}
```

### 2. GetDefectCard
Retrieves defect card information for a specific fleet number.

**Request:**
```json
{
  "action": "GetDefectCard",
  "fleetNumber": "12345"
}
```

### 3. AddDefectCard
Creates a new defect card entry.

**Request:**
```json
{
  "action": "AddDefectCard",
  "fleetNumber": "12345",
  "dutyNumber": "D001",
  "vehicleHeight": "3.5m",
  "depot": "Main Depot"
}
```

### 4. GenerateImageUploadUrl
Generates a presigned URL for uploading images to S3.

**Request:**
```json
{
  "action": "GenerateImageUploadUrl",
  "fileName": "defect-image.jpg",
  "contentType": "image/jpeg"
}
```

### 5. GenerateImageDownloadUrl
Generates a presigned URL for downloading images from S3.

**Request:**
```json
{
  "action": "GenerateImageDownloadUrl",
  "imageKey": "defects/12345/image.jpg"
}
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `BLINK_APPLICATION_API_TOKEN` | API token for Blink service | Yes |
| `DEFECT_CART_TABLE` | DynamoDB table name for defect cards | Yes |
| `IMAGES_BUCKET` | S3 bucket name for image storage | Yes |
| `NODE_ENV` | Environment (development/production) | No |

## Error Handling

The function implements comprehensive error handling with standardized response formats:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "statusCode": 400,
    "timestamp": "2024-01-01T00:00:00.000Z",
    "details": "Additional error details (development only)"
  }
}
```

## Security Features

- **Input Validation**: All inputs are validated and sanitized
- **Authentication**: Requires valid user authentication through Lambda authorizer
- **Authorization**: Permission checks based on user organization
- **Data Sanitization**: Sensitive data is filtered from responses
- **Request Logging**: Comprehensive logging with request IDs for tracing

## Performance Optimizations

- **Connection Reuse**: HTTP client configured for connection pooling
- **Timeout Management**: Appropriate timeouts for external API calls
- **Context Optimization**: `callbackWaitsForEmptyEventLoop = false` for better performance
- **Error Caching**: Proper error handling to prevent unnecessary retries

## Development

### Local Testing
```bash
# Install dependencies
npm install

# Run tests (when implemented)
npm test

# Package for deployment
npm run package
```

### Code Structure
```
├── index.js                 # Main Lambda handler
├── utils/
│   ├── Authenticator.js     # User authentication utilities
│   ├── ResponseHelper.js    # Response formatting utilities
│   └── EventValidator.js    # Input validation utilities
├── package.json             # Dependencies and metadata
└── README.md               # This file
```

## Deployment

This function is deployed via Terraform as part of the BPA infrastructure. See the main infrastructure documentation for deployment instructions.

## Monitoring

- **CloudWatch Logs**: All requests are logged with unique request IDs
- **Metrics**: Standard Lambda metrics available in CloudWatch
- **Tracing**: Request correlation through request IDs

## Contributing

1. Follow AWS Lambda best practices
2. Maintain comprehensive error handling
3. Add appropriate logging for debugging
4. Validate all inputs
5. Update documentation for new features
