// index.js
import getUser from './utils/Authenticator.js'


export async function handler(event) {
  try {
    const eventData = JSON.stringify(event, null, 2);
    console.log("Event:", eventData);

    const userResponse = await getUser(event);
    console.log("User response:", userResponse);
      
    return {
      statusCode: 200,
      body: JSON.stringify(userResponse.data),
    };	
	
  } catch (error) {
    console.error("Insert error:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal error", error: error.message }),
    };
  }
};