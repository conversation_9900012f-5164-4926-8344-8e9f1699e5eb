// index.js
import handleGenerateImageUploadUrl from './handlers/GenerateImageUploadUrl.js'; 
import handleGenerateImageDownloadUrl from './handlers/GenerateImageDownloadUrl.js'; 
import handleAddDefectCard from './handlers/AddDefectCard.js'; 
import handleGetDefectCard from './handlers/GetDefectCard.js'; 
import getValidateUser from './utils/Authenticator.js'

const parseRequestBody = (event) => {
  if (!event) return {};

  try {
    if (typeof event.body === "string") {
      return JSON.parse(event.body);
    } else if (typeof event.body === "object") {
      return event.body;
    } else {
      return event; // in case event is the payload directly
    }
  } catch (e) {
    console.error("Failed to parse request body:", e);
    return {};
  }
};

export async function handler(event) {
	
	
  try {
  const userId = getValidateUser(event);

	const eventData = JSON.stringify(event, null, 2);
	console.log("Event:", eventData);
	  
    const body = parseRequestBody(event);
	const action = body?.action;
	console.log("action:", action);
	
	switch (action) {
      case 'GenerateImageDownloadUrl':
        return await handleGenerateImageDownloadUrl(body);
      case 'GenerateImageUploadUrl':
        return await handleGenerateImageUploadUrl(body);
      case 'GetDefectCard':
        return await handleGetDefectCard(body);
      case 'AddDefectCard':
        return await handleAddDefectCard(body);
      // case 'UpdateDefectCard':
      //   return await handleUpdateDefectCard(body);
      default:
        return {
          statusCode: 400,
          body: JSON.stringify({ error: `Unknown action: ${action}` }),
        };
    }
	
  } catch (error) {
    console.error("Insert error:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal error", error: error.message }),
    };
  }
};