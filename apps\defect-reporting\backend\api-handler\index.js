/**
 * AWS Lambda Handler for BPA Defect Reporting API
 *
 * This Lambda function handles defect reporting operations including:
 * - User authentication and data retrieval
 * - Defect card management
 * - Image upload/download URL generation
 *
 * @param {Object} event - API Gateway Lambda Proxy integration event object
 * @param {Object} context - Lambda Context runtime methods and attributes
 * @returns {Object} API Gateway Lambda Proxy integration response object
 */

import { getUser } from './utils/Authenticator.js';
import { createErrorResponse, createSuccessResponse } from './utils/ResponseHelper.js';
import { validateEvent } from './utils/EventValidator.js';

// Environment variables validation
const requiredEnvVars = ['BLINK_APPLICATION_API_TOKEN', 'DEFECT_CART_TABLE', 'IMAGES_BUCKET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

export const handler = async (event, context) => {
  // Set Lambda context options for better performance
  context.callbackWaitsForEmptyEventLoop = false;

  const requestId = context.awsRequestId;
  const startTime = Date.now();

  try {
    // Log request start with minimal event data for security
    console.log(`[${requestId}] Request started`, {
      httpMethod: event.httpMethod,
      path: event.path,
      userAgent: event.headers?.['user-agent'],
      sourceIp: event.requestContext?.identity?.sourceIp,
      requestId
    });

    // Validate event structure
    const validationResult = validateEvent(event);
    if (!validationResult.isValid) {
      console.warn(`[${requestId}] Event validation failed:`, validationResult.errors);
      return createErrorResponse(400, 'Invalid request format', validationResult.errors);
    }

    // Extract action from request body or query parameters
    const action = event.body ? JSON.parse(event.body).action : event.queryStringParameters?.action;

    if (!action) {
      return createErrorResponse(400, 'Missing required action parameter');
    }

    console.log(`[${requestId}] Processing action: ${action}`);

    // Route to appropriate handler based on action
    let result;
    switch (action.toLowerCase()) {
      case 'getuser':
        result = await handleGetUser(event, requestId);
        break;
      case 'getdefectcard':
        result = await handleGetDefectCard(event, requestId);
        break;
      case 'adddefectcard':
        result = await handleAddDefectCard(event, requestId);
        break;
      case 'generateimageuploadurl':
        result = await handleGenerateImageUploadUrl(event, requestId);
        break;
      case 'generateimagedownloadurl':
        result = await handleGenerateImageDownloadUrl(event, requestId);
        break;
      default:
        return createErrorResponse(400, `Unsupported action: ${action}`);
    }

    const duration = Date.now() - startTime;
    console.log(`[${requestId}] Request completed successfully in ${duration}ms`);

    return createSuccessResponse(result);

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[${requestId}] Request failed after ${duration}ms:`, {
      error: error.message,
      stack: error.stack,
      requestId
    });

    // Don't expose internal error details in production
    const isProduction = process.env.NODE_ENV === 'production';
    const errorMessage = isProduction ? 'Internal server error' : error.message;

    return createErrorResponse(500, errorMessage, isProduction ? undefined : error.stack);
  }
};

/**
 * Handle get user action
 */
async function handleGetUser(event, requestId) {
  console.log(`[${requestId}] Getting user information`);

  const userResponse = await getUser(event);

  console.log(`[${requestId}] User retrieved successfully`);
  return userResponse;
}

/**
 * Handle get defect card action
 */
async function handleGetDefectCard(event, requestId) {
  console.log(`[${requestId}] Getting defect card`);
  // TODO: Implement defect card retrieval logic
  throw new Error('GetDefectCard action not yet implemented');
}

/**
 * Handle add defect card action
 */
async function handleAddDefectCard(event, requestId) {
  console.log(`[${requestId}] Adding defect card`);
  // TODO: Implement defect card creation logic
  throw new Error('AddDefectCard action not yet implemented');
}

/**
 * Handle generate image upload URL action
 */
async function handleGenerateImageUploadUrl(event, requestId) {
  console.log(`[${requestId}] Generating image upload URL`);
  // TODO: Implement S3 presigned URL generation for upload
  throw new Error('GenerateImageUploadUrl action not yet implemented');
}

/**
 * Handle generate image download URL action
 */
async function handleGenerateImageDownloadUrl(event, requestId) {
  console.log(`[${requestId}] Generating image download URL`);
  // TODO: Implement S3 presigned URL generation for download
  throw new Error('GenerateImageDownloadUrl action not yet implemented');
}