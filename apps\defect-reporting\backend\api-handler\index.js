/**
 * AWS Lambda Handler for BPA Defect Reporting API
 *
 * This Lambda function handles defect reporting operations including:
 * - User authentication and data retrieval
 * - Defect card management
 * - Image upload/download URL generation
 *
 * @param {Object} event - API Gateway Lambda Proxy integration event object
 * @param {Object} context - Lambda Context runtime methods and attributes
 * @returns {Object} API Gateway Lambda Proxy integration response object
 */

import { getUser } from './utils/Authenticator.js';
import { createErrorResponse, createSuccessResponse } from './utils/ResponseHelper.js';
import { validateEvent } from './utils/EventValidator.js';

// Environment variables validation
const requiredEnvVars = ['BLINK_APPLICATION_API_TOKEN'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

export const handler = async (event, context) => {
  // Set Lambda context options for better performance
  context.callbackWaitsForEmptyEventLoop = false;

  const requestId = context.awsRequestId;
  const startTime = Date.now();

  try {
    // Log request start with minimal event data for security
    console.log(`[${requestId}] Request started`, {
      httpMethod: event.httpMethod,
      path: event.path,
      userAgent: event.headers?.['user-agent'],
      sourceIp: event.requestContext?.identity?.sourceIp,
      requestId
    });

    // Validate event structure
    const validationResult = validateEvent(event);
    if (!validationResult.isValid) {
      console.warn(`[${requestId}] Event validation failed:`, validationResult.errors);
      return createErrorResponse(400, 'Invalid request format', validationResult.errors);
    }

    result = await handleGetUser(event, requestId);

    const duration = Date.now() - startTime;
    console.log(`[${requestId}] Request completed successfully in ${duration}ms`);

    return createSuccessResponse(result);

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[${requestId}] Request failed after ${duration}ms:`, {
      error: error.message,
      stack: error.stack,
      requestId
    });

    // Don't expose internal error details in production
    const isProduction = process.env.NODE_ENV === 'production';
    const errorMessage = isProduction ? 'Internal server error' : error.message;

    return createErrorResponse(500, errorMessage, isProduction ? undefined : error.stack);
  }
};

/**
 * Handle get user action
 */
async function handleGetUser(event, requestId) {
  console.log(`[${requestId}] Getting user information`);

  const userResponse = await getUser(event);

  console.log(`[${requestId}] User retrieved successfully`);
  return userResponse;
}
