# BPA Defect Reporting System - Development Guide

## Development Environment Setup

### Prerequisites

- **Node.js**: >= 22.0.0
- **npm**: >= 10.0.0
- **AWS CLI**: Latest version, configured with credentials
- **Terraform**: >= 1.0.0
- **Git**: Latest version

### Initial Setup

1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd bpa-defect-reporting-system
   ```

2. **Install Dependencies**
   ```bash
   npm run install:all
   ```

3. **Configure AWS Credentials**
   ```bash
   aws configure
   # or use AWS SSO, environment variables, or IAM roles
   ```

4. **Setup Terraform**
   ```bash
   cd infrastructure
   cp terraform.tfvars.example terraform.tfvars
   # Edit terraform.tfvars with your values
   terraform init
   ```

## Project Structure

```
bpa/
├── README.md                           # Project overview
├── ARCHITECTURE.md                     # System architecture
├── DEVELOPMENT.md                      # This file
├── package.json                        # Root package configuration
├── .gitignore                         # Git ignore patterns
├── apps/                              # Application components
│   └── defect-reporting/
│       └── backend/
│           └── api-handler/           # Main API Lambda
│               ├── index.js           # Main handler
│               ├── utils/             # Utility modules
│               ├── package.json       # Dependencies
│               └── README.md          # API documentation
├── authentication/                    # Authentication module
│   ├── authorizer-lambda.js          # Lambda authorizer
│   ├── package.json                  # ES module config
│   └── README.md                     # Auth documentation

├── infrastructure/                    # Infrastructure as Code
│   ├── main.tf                       # Terraform config
│   ├── terraform.tfvars.example      # Example variables
│   └── README.md                     # Infrastructure docs
└── driver-defects-app/              # Frontend application
    └── build/                        # Built assets
```

## Development Workflow

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes to code
# Test locally (see testing section)

# Validate changes
npm run validate
npm run lint
npm run test

# Commit changes
git add .
git commit -m "feat: add your feature description"

# Push and create PR
git push origin feature/your-feature-name
```

### 2. Infrastructure Changes

```bash
# Plan infrastructure changes
npm run plan:dev

# Apply to development environment
npm run deploy:dev

# Test the changes
# If successful, apply to staging
npm run deploy:staging

# After validation, apply to production
npm run deploy:prod
```

### 3. Lambda Function Development

#### API Handler Development

```bash
cd apps/defect-reporting/backend/api-handler

# Install dependencies
npm install

# Run tests
npm test

# Package for deployment
npm run package
```

#### Authentication Development

```bash
cd authentication

# No dependencies to install (ES modules only)
# Test the function logic
node -e "import('./authorizer-lambda.js').then(m => console.log('Syntax OK'))"

# Package for deployment
npm run package:auth
```



## Testing

### Unit Testing

```bash
# Run all tests
npm test

# Run specific component tests
npm run test:api-handler
```

### Integration Testing

```bash
# Test API endpoints
# Get User Information
curl -X POST \
  "https://{api-gateway-id}.execute-api.{region}.amazonaws.com/dev/defect-reporting" \
  -H "Content-Type: application/json" \
  -H "blink-auth-proxysecret: your-secret" \
  -H "blink-auth-userid: test-user" \
  -d '{"action": "GetUser"}'

# Submit Defect Report
curl -X POST \
  "https://{api-gateway-id}.execute-api.{region}.amazonaws.com/{env}/defect-reporting" \
  -H "Content-Type: application/json" \
  -H "blink-auth-proxysecret: your-secret" \
  -H "blink-auth-userid: user-id" \
  -d '{
    "action": "SubmitDefectReport",
    "defectData": {
      "fleetNumber": "12345",
      "description": "Brake light not working",
      "severity": "high",
      "location": "Rear left"
    }
  }'
```

### Infrastructure Testing

```bash
# Validate Terraform configuration
npm run validate:terraform

# Plan infrastructure changes
terraform plan

# Test in development environment
npm run deploy:dev
```

## Code Standards

### JavaScript/Node.js Standards

1. **ES Modules**: Use `import/export` syntax
2. **Async/Await**: Prefer async/await over promises
3. **Error Handling**: Comprehensive try/catch blocks
4. **Logging**: Structured logging with request IDs
5. **Documentation**: JSDoc comments for all functions
6. **Validation**: Input validation and sanitization

### Example Function Structure

```javascript
/**
 * Function description
 * @param {Object} param - Parameter description
 * @returns {Promise<Object>} Return value description
 */
export async function exampleFunction(param) {
  const requestId = generateRequestId();
  
  try {
    console.log(`[${requestId}] Function started`);
    
    // Validate inputs
    const validation = validateInput(param);
    if (!validation.isValid) {
      throw new Error(`Invalid input: ${validation.errors.join(', ')}`);
    }
    
    // Process logic
    const result = await processLogic(param);
    
    console.log(`[${requestId}] Function completed successfully`);
    return result;
    
  } catch (error) {
    console.error(`[${requestId}] Function failed:`, error);
    throw error;
  }
}
```

### Terraform Standards

1. **Resource Naming**: Consistent naming with environment suffix
2. **Tagging**: All resources tagged with Environment and Project
3. **Variables**: Use variables for all configurable values
4. **Outputs**: Provide useful outputs for other modules
5. **Documentation**: Comments for complex configurations

## Environment Management

### Environment Variables

| Environment | Purpose | Configuration |
|-------------|---------|---------------|
| `dev` | Development and testing | Relaxed security, verbose logging |
| `staging` | Pre-production validation | Production-like, limited access |
| `prod` | Production system | High security, minimal logging |

### Configuration Management

```bash
# Switch Terraform workspace
terraform workspace select dev
terraform workspace select staging
terraform workspace select prod

# Environment-specific variables
# Edit terraform.tfvars for each environment
```

## Debugging

### CloudWatch Logs

```bash
# View Lambda logs
aws logs tail /aws/lambda/bpa-authorizer-lambda-dev --follow
aws logs tail /aws/lambda/bpa-defect-reporting-api-handler-gabo-dev --follow
```

### API Gateway Testing

```bash
# Test with AWS CLI
aws apigateway test-invoke-method \
  --rest-api-id {api-id} \
  --resource-id {resource-id} \
  --http-method POST \
  --headers '{"blink-auth-proxysecret":"secret","blink-auth-userid":"user"}' \
  --body '{"action":"GetUser"}'
```

### Local Development

```bash
# Run Lambda functions locally (using SAM or similar)
sam local start-api

# Test individual functions
sam local invoke ApiHandlerFunction --event test-event.json
```

## Performance Optimization

### Lambda Optimization

1. **Memory Allocation**: Right-size memory for performance/cost balance
2. **Timeout Settings**: Set appropriate timeouts
3. **Cold Start Reduction**: Minimize initialization code
4. **Connection Reuse**: Reuse HTTP connections and database connections

### API Gateway Optimization

1. **Caching**: Enable caching where appropriate
2. **Compression**: Enable response compression
3. **Throttling**: Set appropriate throttling limits

### Database Optimization

1. **Query Patterns**: Optimize DynamoDB queries
2. **Indexing**: Use Global Secondary Indexes effectively
3. **Batch Operations**: Use batch operations for multiple items

## Security Best Practices

### Code Security

1. **Input Validation**: Validate all inputs
2. **Output Sanitization**: Sanitize all outputs
3. **Secret Management**: Use environment variables for secrets
4. **Logging**: Never log sensitive information
5. **Dependencies**: Keep dependencies updated

### Infrastructure Security

1. **IAM Roles**: Use least-privilege principles
2. **VPC**: Consider VPC deployment for sensitive workloads
3. **Encryption**: Enable encryption at rest and in transit
4. **Monitoring**: Set up security monitoring and alerts

## Deployment

### Automated Deployment

```bash
# Development deployment
npm run deploy:dev

# Staging deployment
npm run deploy:staging

# Production deployment (requires approval)
npm run deploy:prod
```

### Manual Deployment

```bash
cd infrastructure

# Initialize Terraform
terraform init

# Select workspace
terraform workspace select dev

# Plan changes
terraform plan

# Apply changes
terraform apply
```

### Rollback Procedures

```bash
# Rollback to previous Terraform state
terraform apply -target=resource_name

# Rollback Lambda function
aws lambda update-function-code \
  --function-name function-name \
  --zip-file fileb://previous-version.zip
```

## Monitoring and Alerting

### CloudWatch Dashboards

Create dashboards for:
- Lambda function metrics
- API Gateway metrics
- DynamoDB metrics
- Error rates and latencies

### Alerts

Set up alerts for:
- High error rates
- Unusual latency
- Failed deployments
- Security events

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check authentication headers and secrets
2. **500 Internal Error**: Check Lambda function logs
3. **Timeout Errors**: Check function timeout settings
4. **Permission Denied**: Check IAM roles and policies

### Debug Checklist

- [ ] Check CloudWatch logs for errors
- [ ] Verify environment variables are set
- [ ] Confirm IAM permissions are correct
- [ ] Test with known good inputs
- [ ] Check API Gateway configuration
- [ ] Verify Terraform state is consistent

## Contributing

### Pull Request Process

1. Create feature branch from main
2. Make changes following code standards
3. Add/update tests as needed
4. Update documentation
5. Run validation and tests
6. Create pull request with clear description
7. Address review feedback
8. Merge after approval

### Code Review Guidelines

- Check for security vulnerabilities
- Verify error handling is comprehensive
- Ensure logging is appropriate
- Confirm tests cover new functionality
- Review documentation updates

## Resources

### Documentation

- [AWS Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
- [API Gateway Developer Guide](https://docs.aws.amazon.com/apigateway/latest/developerguide/)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs)
- [Node.js Best Practices](https://github.com/goldbergyoni/nodebestpractices)

### Tools

- [AWS CLI](https://aws.amazon.com/cli/)
- [Terraform](https://www.terraform.io/)
- [AWS SAM](https://aws.amazon.com/serverless/sam/)
- [Postman](https://www.postman.com/) for API testing
