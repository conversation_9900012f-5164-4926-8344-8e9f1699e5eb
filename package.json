{"name": "bpa-defect-reporting-system", "version": "1.0.0", "description": "Bus Performance Analytics - Defect Reporting System", "private": true, "type": "module", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "scripts": {"install:all": "npm run install:api-handler && npm run install:auth && npm run install:web-proxy", "install:api-handler": "cd apps/defect-reporting/backend/api-handler && npm install", "install:auth": "echo 'No dependencies for authentication module'", "install:web-proxy": "echo 'No dependencies for web-proxy module'", "test": "npm run test:api-handler", "test:api-handler": "cd apps/defect-reporting/backend/api-handler && npm test", "lint": "npm run lint:api-handler", "lint:api-handler": "cd apps/defect-reporting/backend/api-handler && npm run lint", "package:all": "npm run package:api-handler && npm run package:auth && npm run package:web-proxy", "package:api-handler": "cd apps/defect-reporting/backend/api-handler && npm run package", "package:auth": "cd authentication && zip -r ../infrastructure/authorizer-lambda.zip . -x .git/\\* *.md", "package:web-proxy": "cd web-proxy && zip -r ../infrastructure/web-proxy-lambda.zip . -x .git/\\* *.md", "deploy:dev": "cd infrastructure && terraform workspace select dev && terraform apply", "deploy:staging": "cd infrastructure && terraform workspace select staging && terraform apply", "deploy:prod": "cd infrastructure && terraform workspace select prod && terraform apply", "plan:dev": "cd infrastructure && terraform workspace select dev && terraform plan", "plan:staging": "cd infrastructure && terraform workspace select staging && terraform plan", "plan:prod": "cd infrastructure && terraform workspace select prod && terraform plan", "clean": "npm run clean:packages && npm run clean:node-modules", "clean:packages": "rm -f infrastructure/*.zip", "clean:node-modules": "find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +", "docs:serve": "echo 'Documentation server not configured'", "validate": "npm run validate:terraform && npm run validate:json", "validate:terraform": "cd infrastructure && terraform validate", "validate:json": "find . -name '*.json' -not -path './node_modules/*' -exec node -e 'JSON.parse(require(\"fs\").readFileSync(process.argv[1]))' {} \\;", "security:audit": "npm audit --audit-level moderate", "format": "echo 'Code formatting not configured'", "pre-commit": "npm run validate && npm run lint && npm run test"}, "workspaces": ["apps/defect-reporting/backend/api-handler"], "keywords": ["aws", "serverless", "lambda", "api-gateway", "defect-reporting", "bus-analytics", "terraform", "infrastructure"], "author": "BPA Development Team", "license": "UNLICENSED", "repository": {"type": "git", "url": "git+https://github.com/your-org/bpa.git"}, "bugs": {"url": "https://github.com/your-org/bpa/issues"}, "homepage": "https://github.com/your-org/bpa#readme", "devDependencies": {}, "dependencies": {}, "config": {"terraform": {"version": ">=1.0.0"}, "aws": {"region": "us-east-1"}}, "directories": {"doc": "docs", "test": "tests"}}