# BPA Web Proxy Lambda

AWS Lambda function that proxies web requests to CloudFront while adding authentication cookies and managing request/response headers.

## Overview

This Lambda function serves as a proxy between API Gateway and CloudFront, providing:

- **Authentication Cookie Management**: Adds secure authentication cookies to responses
- **Header Processing**: Forwards appropriate headers and adds original request headers to responses
- **Content Handling**: Supports both text and binary content with proper encoding
- **Compression Management**: Handles compression-related headers to prevent issues
- **Retry Logic**: Implements exponential backoff for CloudFront requests
- **Request Correlation**: Provides request IDs for debugging and monitoring

## Architecture

```
Client → API Gateway → Lambda Authorizer → Web Proxy Lambda → CloudFront
                                              ↓
                                         Response with cookies
```

## Features

### 🔐 Authentication Cookie Management

The function automatically adds secure authentication cookies to all responses:

- **`_BAT`**: User authentication token with user ID
- **`session_id`**: Unique session identifier
- **`auth_timestamp`**: Authentication timestamp

All cookies are configured with security flags:
- `HttpOnly`: Prevents JavaScript access
- `Secure`: HTTPS only
- `SameSite=Strict`: CSRF protection
- `Path=/`: Available across the entire domain

### 📡 Header Processing

#### Request Headers
- Filters out sensitive headers (auth tokens, internal AWS headers)
- Forwards safe headers to CloudFront
- Adds compression management headers

#### Response Headers
- Preserves CloudFront response headers
- Adds original request headers with `x-original-` prefix
- Includes processing metadata (`X-Request-ID`, `X-Processing-Time`)

### 🔄 Retry Logic

Implements robust retry mechanism:
- **Maximum Retries**: 3 attempts
- **Exponential Backoff**: 2^attempt * 1000ms delay
- **Timeout Handling**: 30-second request timeout
- **Error Logging**: Comprehensive error tracking

### 📊 Content Handling

#### Text Content
- UTF-8 encoding for text responses
- Supports HTML, CSS, JavaScript, JSON, XML

#### Binary Content
- Base64 encoding for binary responses
- Supports images, documents, media files
- Automatic content type detection

## Configuration

### Environment Variables

Currently no environment variables are required, but the function is designed to support configuration through environment variables if needed.

### Constants

```javascript
const CONFIG = {
  CLOUDFRONT_URL: 'https://d2h85rpj1ghd7n.cloudfront.net',
  REQUEST_TIMEOUT: 30000, // 30 seconds
  MAX_RETRIES: 3,
  COOKIE_OPTIONS: {
    httpOnly: true,
    secure: true,
    sameSite: 'Strict',
    path: '/'
  }
};
```

## API

### Input Event

The function expects an API Gateway Lambda Proxy integration event:

```javascript
{
  "httpMethod": "GET",
  "path": "/",
  "pathParameters": { "proxy": "dashboard" },
  "queryStringParameters": { "param": "value" },
  "headers": {
    "user-agent": "Mozilla/5.0...",
    "accept": "text/html,application/xhtml+xml...",
    // ... other headers
  },
  "body": null,
  "requestContext": {
    "authorizer": {
      "principalId": "user123"
    }
  }
}
```

### Output Response

Returns an API Gateway Lambda Proxy integration response:

```javascript
{
  "statusCode": 200,
  "headers": {
    "content-type": "text/html",
    "x-original-user-agent": "Mozilla/5.0...",
    "Set-Cookie": "_BAT=user123; Path=/; HttpOnly; Secure; SameSite=Strict, session_id=abc123; Path=/; HttpOnly; Secure; SameSite=Strict",
    "X-Request-ID": "12345678-1234-1234-1234-123456789012",
    "X-Processing-Time": "150ms"
  },
  "body": "<html>...</html>",
  "isBase64Encoded": false
}
```

## Error Handling

### Error Types

1. **Network Errors**: Connection issues with CloudFront
2. **Timeout Errors**: Request exceeds 30-second timeout
3. **HTTP Errors**: Non-2xx responses from CloudFront
4. **Processing Errors**: Internal function errors

### Error Response

```javascript
{
  "statusCode": 500,
  "headers": {
    "Content-Type": "application/json",
    "X-Request-ID": "request-id",
    "X-Processing-Time": "5000ms"
  },
  "body": {
    "success": false,
    "error": {
      "message": "Web proxy error",
      "requestId": "request-id",
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

## Monitoring

### CloudWatch Logs

All requests are logged with structured information:

```
[request-id] Web proxy request started { httpMethod: 'GET', path: '/', ... }
[request-id] Fetching from CloudFront: https://d2h85rpj1ghd7n.cloudfront.net/
[request-id] CloudFront request attempt 1/3
[request-id] CloudFront response: 200
[request-id] Request completed successfully in 150ms
```

### Metrics

- **Duration**: Request processing time
- **Errors**: Failed requests and retry attempts
- **Status Codes**: CloudFront response status distribution

## Security

### Header Filtering

Sensitive headers are automatically filtered:
- `blink-auth-proxysecret`
- `blink-auth-userid`
- `authorization`
- `x-forwarded-*`
- `x-amzn-trace-id`

### Cookie Security

All cookies include security flags:
- Prevents XSS attacks (HttpOnly)
- Requires HTTPS (Secure)
- Prevents CSRF (SameSite=Strict)

### Request Correlation

Each request includes a unique request ID for security auditing and debugging.

## Development

### Local Testing

```bash
# No dependencies to install (uses only Node.js built-ins)
node index.js
```

### Deployment

The function is deployed via Terraform as part of the BPA infrastructure.

## Performance

### Optimizations

- **Connection Reuse**: Lambda container reuse for subsequent requests
- **Efficient Buffering**: Streaming for large responses
- **Timeout Management**: Prevents hanging requests
- **Compression Handling**: Optimized for CloudFront integration

### Benchmarks

- **Cold Start**: ~100-200ms
- **Warm Requests**: ~50-150ms
- **Large Files**: Supports files up to Lambda limits (6MB response)

## Troubleshooting

### Common Issues

1. **Timeout Errors**: Check CloudFront availability
2. **Cookie Issues**: Verify HTTPS and domain configuration
3. **Binary Content**: Ensure proper base64 encoding
4. **Header Conflicts**: Check for duplicate headers

### Debug Information

Enable detailed logging by checking CloudWatch logs for the specific request ID.

## Contributing

1. Follow AWS Lambda best practices
2. Maintain comprehensive error handling
3. Add appropriate logging for debugging
4. Test with both text and binary content
5. Update documentation for changes
