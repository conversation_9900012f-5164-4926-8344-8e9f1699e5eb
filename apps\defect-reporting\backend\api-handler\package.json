{"name": "bpa-defect-reporting-api-handler", "version": "1.0.0", "description": "AWS Lambda function for BPA defect reporting API operations", "type": "module", "main": "index.js", "engines": {"node": ">=22.0.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured\"", "package": "zip -r ../api-handler-lambda.zip . -x node_modules/\\* .git/\\* *.md"}, "keywords": ["aws", "lambda", "api", "defect-reporting", "bpa"], "author": "BPA Development Team", "license": "UNLICENSED", "dependencies": {"axios": "^1.11.0"}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/your-org/bpa.git"}, "bugs": {"url": "https://github.com/your-org/bpa/issues"}, "homepage": "https://github.com/your-org/bpa#readme"}