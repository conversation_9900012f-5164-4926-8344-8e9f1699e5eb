/**
 * Event Validation Utilities for AWS Lambda API Gateway Integration
 * 
 * Validates incoming API Gateway events to ensure they have the required structure
 */

/**
 * Validate API Gateway Lambda Proxy integration event
 * @param {Object} event - The API Gateway event object
 * @returns {Object} Validation result with isValid boolean and errors array
 */
export function validateEvent(event) {
  const errors = [];

  // Check if event exists
  if (!event) {
    errors.push('Event object is null or undefined');
    return { isValid: false, errors };
  }

  // Validate required top-level properties
  const requiredProperties = ['httpMethod', 'path', 'headers', 'requestContext'];
  
  requiredProperties.forEach(prop => {
    if (!(prop in event)) {
      errors.push(`Missing required property: ${prop}`);
    }
  });

  // Validate HTTP method
  if (event.httpMethod) {
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'];
    if (!validMethods.includes(event.httpMethod.toUpperCase())) {
      errors.push(`Invalid HTTP method: ${event.httpMethod}`);
    }
  }

  // Validate path
  if (event.path !== undefined && typeof event.path !== 'string') {
    errors.push('Path must be a string');
  }

  // Validate headers
  if (event.headers !== null && typeof event.headers !== 'object') {
    errors.push('Headers must be an object or null');
  }

  // Validate request context
  if (event.requestContext && typeof event.requestContext !== 'object') {
    errors.push('RequestContext must be an object');
  }

  // Validate body if present
  if (event.body !== null && event.body !== undefined) {
    if (typeof event.body !== 'string') {
      errors.push('Body must be a string or null');
    } else if (event.body.trim().length > 0) {
      // Try to parse JSON body if it's not empty
      try {
        JSON.parse(event.body);
      } catch (parseError) {
        errors.push(`Invalid JSON in body: ${parseError.message}`);
      }
    }
  }

  // Validate query string parameters
  if (event.queryStringParameters !== null && typeof event.queryStringParameters !== 'object') {
    errors.push('QueryStringParameters must be an object or null');
  }

  // Validate path parameters
  if (event.pathParameters !== null && typeof event.pathParameters !== 'object') {
    errors.push('PathParameters must be an object or null');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate specific action parameters
 * @param {string} action - The action to validate
 * @param {Object} parameters - The parameters for the action
 * @returns {Object} Validation result with isValid boolean and errors array
 */
export function validateActionParameters(action, parameters) {
  const errors = [];

  if (!action || typeof action !== 'string') {
    errors.push('Action must be a non-empty string');
    return { isValid: false, errors };
  }

  const actionLower = action.toLowerCase();

  switch (actionLower) {
    case 'getuser':
      // No additional parameters required for getuser
      break;

    case 'getdefectcard':
      if (!parameters.fleetNumber) {
        errors.push('fleetNumber is required for getdefectcard action');
      }
      break;

    case 'adddefectcard':
      const requiredFields = ['fleetNumber', 'dutyNumber', 'vehicleHeight', 'depot'];
      requiredFields.forEach(field => {
        if (!parameters[field]) {
          errors.push(`${field} is required for adddefectcard action`);
        }
      });
      break;

    case 'generateimageuploadurl':
      if (!parameters.fileName) {
        errors.push('fileName is required for generateimageuploadurl action');
      }
      if (!parameters.contentType) {
        errors.push('contentType is required for generateimageuploadurl action');
      }
      break;

    case 'generateimagedownloadurl':
      if (!parameters.imageKey) {
        errors.push('imageKey is required for generateimagedownloadurl action');
      }
      break;

    default:
      errors.push(`Unknown action: ${action}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitize and validate user input
 * @param {*} input - Input to sanitize
 * @param {string} type - Expected type ('string', 'number', 'boolean', 'object', 'array')
 * @param {Object} options - Validation options
 * @returns {Object} Sanitized input and validation result
 */
export function sanitizeInput(input, type, options = {}) {
  const { maxLength = 1000, allowEmpty = false, pattern = null } = options;
  const errors = [];

  if (input === null || input === undefined) {
    if (!allowEmpty) {
      errors.push('Input cannot be null or undefined');
    }
    return { value: input, isValid: errors.length === 0, errors };
  }

  let sanitizedValue = input;

  switch (type) {
    case 'string':
      if (typeof input !== 'string') {
        errors.push('Input must be a string');
        break;
      }
      
      // Trim whitespace
      sanitizedValue = input.trim();
      
      // Check length
      if (sanitizedValue.length > maxLength) {
        errors.push(`Input exceeds maximum length of ${maxLength} characters`);
      }
      
      // Check if empty when not allowed
      if (!allowEmpty && sanitizedValue.length === 0) {
        errors.push('Input cannot be empty');
      }
      
      // Check pattern if provided
      if (pattern && !pattern.test(sanitizedValue)) {
        errors.push('Input does not match required pattern');
      }
      
      break;

    case 'number':
      const num = Number(input);
      if (isNaN(num)) {
        errors.push('Input must be a valid number');
      } else {
        sanitizedValue = num;
      }
      break;

    case 'boolean':
      if (typeof input === 'boolean') {
        sanitizedValue = input;
      } else if (typeof input === 'string') {
        const lowerInput = input.toLowerCase();
        if (['true', '1', 'yes'].includes(lowerInput)) {
          sanitizedValue = true;
        } else if (['false', '0', 'no'].includes(lowerInput)) {
          sanitizedValue = false;
        } else {
          errors.push('Input must be a valid boolean value');
        }
      } else {
        errors.push('Input must be a boolean or boolean string');
      }
      break;

    case 'object':
      if (typeof input !== 'object' || Array.isArray(input)) {
        errors.push('Input must be an object');
      }
      break;

    case 'array':
      if (!Array.isArray(input)) {
        errors.push('Input must be an array');
      }
      break;

    default:
      errors.push(`Unknown validation type: ${type}`);
  }

  return {
    value: sanitizedValue,
    isValid: errors.length === 0,
    errors
  };
}
