{"version": 4, "terraform_version": "1.9.8", "serial": 113, "lineage": "78f53786-a1e1-bbca-ebe7-b6fadfc1e245", "outputs": {"api_gateway_id": {"value": "zkvfa0mljb", "type": "string"}, "api_gateway_url": {"value": "https://zkvfa0mljb.execute-api.eu-central-1.amazonaws.com/gabo-dev", "type": "string"}, "api_handler_lambda_function_name": {"value": "bpa-defect-reporting-api-handler-gabo-gabo-dev", "type": "string"}, "authorizer_lambda_function_name": {"value": "bpa-authorizer-lambda-gabo-dev", "type": "string"}, "defect_reporting_endpoint_url": {"value": "https://zkvfa0mljb.execute-api.eu-central-1.amazonaws.com/gabo-dev/defect-reporting", "type": "string"}, "defects_endpoint_url": {"value": "https://zkvfa0mljb.execute-api.eu-central-1.amazonaws.com/gabo-dev/defects", "type": "string"}, "dynamodb_table_name": {"value": "bpa-defect-cart-gabo-dev", "type": "string"}, "s3_bucket_name": {"value": "bpa-defect-images-gabo-dev", "type": "string"}, "web_proxy_lambda_function_name": {"value": "bpa-web-proxy-lambda-gabo-dev", "type": "string"}, "web_root_endpoint_url": {"value": "https://zkvfa0mljb.execute-api.eu-central-1.amazonaws.com/gabo-dev/", "type": "string"}}, "resources": [{"mode": "data", "type": "archive_file", "name": "api_handler_lambda_zip", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"exclude_symlink_directories": null, "excludes": ["*.md", ".git", "node_modules"], "id": "db89630f878ee2e312404fb01ad147627991bb6e", "output_base64sha256": "M5xsqgHlJz5PSJtY85dOjEnzbbut7u1PnEnBrAW2l7U=", "output_base64sha512": "+xJUOIoONzzc7H3y97YJ4N7X6QiCn4+1mffwJpNLse0ys9N0oM8hO0aQo+ixuWjTh1NJcFOvf9DkXjb/RIkkhg==", "output_file_mode": null, "output_md5": "028ef351029bb542f8c937ca0b35df28", "output_path": "./api-handler-lambda.zip", "output_sha": "db89630f878ee2e312404fb01ad147627991bb6e", "output_sha256": "339c6caa01e5273e4f489b58f3974e8c49f36dbbadeeed4f9c49c1ac05b697b5", "output_sha512": "fb1254388a0e373cdcec7df2f7b609e0ded7e908829f8fb599f7f026934bb1ed32b3d374a0cf213b4690a3e8b1b968d38753497053af7fd0e45e36ff44892486", "output_size": 6207, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "./../apps/defect-reporting/backend/api-handler", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "archive_file", "name": "authorizer_lambda_zip", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"exclude_symlink_directories": null, "excludes": ["*.md", ".git"], "id": "9e9a1a36ebdf69549498723cf5b042d4834c272d", "output_base64sha256": "fHHVErpwa84x+POUbOEjJN7jYUSaI+iMMsk88oFd0aM=", "output_base64sha512": "UkSEiBT5ajyEzS/mUQOZsn0TObF7iuxixPHz7TAE6E8OYvUnh00BaOV0XAMqKncnnjRmzLUtOCpObUrXkwSang==", "output_file_mode": null, "output_md5": "5d061314fa96b06dc733d0bb7336ceb8", "output_path": "./authorizer-lambda.zip", "output_sha": "9e9a1a36ebdf69549498723cf5b042d4834c272d", "output_sha256": "7c71d512ba706bce31f8f3946ce12324dee361449a23e88c32c93cf2815dd1a3", "output_sha512": "5244848814f96a3c84cd2fe6510399b27d1339b17b8aec62c4f1f3ed3004e84f0e62f527874d0168e5745c032a2a77279e3466ccb52d382a4e6d4ad793049a9e", "output_size": 1255, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "./../authentication", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "archive_file", "name": "web_proxy_lambda_zip", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"exclude_symlink_directories": null, "excludes": ["*.md", ".git"], "id": "4e030f1f1059e329d6398e5fc93baa6264e0238e", "output_base64sha256": "g9q5ZZ1mrt2BQ/r6aocpsQJ+vvglY9TIr0qP4yMT1o0=", "output_base64sha512": "qY/aevBwiyzyPDM7KHoFCCaqdL2rjyHAqyXN6+nbQR53uJEqfWv7IE0eDlEVrLeWMC6Tm4TnpVWqCa+nGay6tg==", "output_file_mode": null, "output_md5": "c23788b30524ecd9accde69b587b6d00", "output_path": "./web-proxy-lambda.zip", "output_sha": "4e030f1f1059e329d6398e5fc93baa6264e0238e", "output_sha256": "83dab9659d66aedd8143fafa6a8729b1027ebef82563d4c8af4a8fe32313d68d", "output_sha512": "a98fda7af0708b2cf23c333b287a050826aa74bdab8f21c0ab25cdebe9db411e77b8912a7d6bfb204d1e0e5115acb796302e939b84e7a555aa09afa719acbab6", "output_size": 2107, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "./../web-proxy", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/<PERSON><PERSON>", "id": "************", "user_id": "AIDAVRBZJFBMPA6MIVZQP"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_api_gateway_authorizer", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:apigateway:eu-central-1::/restapis/zkvfa0mljb/authorizers/47cejl", "authorizer_credentials": "arn:aws:iam::************:role/bpa-api-gateway-authorizer-role-gabo-dev", "authorizer_result_ttl_in_seconds": 0, "authorizer_uri": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-authorizer-lambda-gabo-dev/invocations", "id": "47cejl", "identity_source": "", "identity_validation_expression": "", "name": "bpa-lambda-authorizer-gabo-dev", "provider_arns": [], "rest_api_id": "zkvfa0mljb", "type": "REQUEST"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_deployment", "name": "bpa_api_deployment", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"canary_settings": [], "created_date": "2025-07-18T09:04:44Z", "description": "", "execution_arn": "arn:aws:execute-api:eu-central-1:************:zkvfa0mljb/gabo-dev", "id": "0hpiqm", "invoke_url": "https://zkvfa0mljb.execute-api.eu-central-1.amazonaws.com/gabo-dev", "rest_api_id": "zkvfa0mljb", "stage_description": null, "stage_name": "gabo-dev", "triggers": null, "variables": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_integration.defect_reporting_post_integration", "aws_api_gateway_integration.defects_get_integration", "aws_api_gateway_integration.defects_post_integration", "aws_api_gateway_integration.root_integration", "aws_api_gateway_integration.web_proxy_integration", "aws_api_gateway_method.defect_reporting_post", "aws_api_gateway_method.defects_get", "aws_api_gateway_method.defects_post", "aws_api_gateway_method.root_any", "aws_api_gateway_method.web_proxy_any", "aws_api_gateway_resource.defect_reporting_resource", "aws_api_gateway_resource.defects_resource", "aws_api_gateway_resource.web_proxy_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.api_gateway_web_proxy_role", "aws_iam_role.api_handler_lambda_role", "aws_iam_role.authorizer_lambda_role", "aws_iam_role.web_proxy_lambda_role", "aws_lambda_function.api_handler_lambda", "aws_lambda_function.authorizer_lambda", "aws_lambda_function.web_proxy_lambda", "data.archive_file.api_handler_lambda_zip", "data.archive_file.authorizer_lambda_zip", "data.archive_file.web_proxy_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "defect_reporting_post_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "azeebm", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-zkvfa0mljb-azeebm-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": {}, "request_templates": {}, "resource_id": "azeebm", "rest_api_id": "zkvfa0mljb", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-defect-reporting-api-handler-gabo-gabo-dev/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_method.defect_reporting_post", "aws_api_gateway_resource.defect_reporting_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.api_handler_lambda_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.api_handler_lambda", "aws_lambda_function.authorizer_lambda", "data.archive_file.api_handler_lambda_zip", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "defects_get_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "xset15", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "GET", "id": "agi-zkvfa0mljb-xset15-GET", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": {}, "request_templates": {}, "resource_id": "xset15", "rest_api_id": "zkvfa0mljb", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-defect-reporting-api-handler-gabo-gabo-dev/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_method.defects_get", "aws_api_gateway_resource.defects_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.api_handler_lambda_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.api_handler_lambda", "aws_lambda_function.authorizer_lambda", "data.archive_file.api_handler_lambda_zip", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "defects_post_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "xset15", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-zkvfa0mljb-xset15-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": {}, "request_templates": {}, "resource_id": "xset15", "rest_api_id": "zkvfa0mljb", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-defect-reporting-api-handler-gabo-gabo-dev/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_method.defects_post", "aws_api_gateway_resource.defects_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.api_handler_lambda_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.api_handler_lambda", "aws_lambda_function.authorizer_lambda", "data.archive_file.api_handler_lambda_zip", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "root_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "p7bigfhonl", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "arn:aws:iam::************:role/bpa-api-gateway-web-proxy-role-gabo-dev", "http_method": "ANY", "id": "agi-zkvfa0mljb-p7bigfhonl-ANY", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": null, "request_templates": null, "resource_id": "p7bigfhonl", "rest_api_id": "zkvfa0mljb", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-web-proxy-lambda-gabo-dev/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_method.root_any", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.api_gateway_web_proxy_role", "aws_iam_role.authorizer_lambda_role", "aws_iam_role.web_proxy_lambda_role", "aws_lambda_function.authorizer_lambda", "aws_lambda_function.web_proxy_lambda", "data.archive_file.authorizer_lambda_zip", "data.archive_file.web_proxy_lambda_zip"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "web_proxy_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "omro0b", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "ANY", "id": "agi-zkvfa0mljb-omro0b-ANY", "integration_http_method": "ANY", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": {"integration.request.path.proxy": "method.request.path.proxy"}, "request_templates": {}, "resource_id": "omro0b", "rest_api_id": "zkvfa0mljb", "timeout_milliseconds": 29000, "tls_config": [], "type": "HTTP_PROXY", "uri": "https://d2h85rpj1ghd7n.cloudfront.net/{proxy}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_method.web_proxy_any", "aws_api_gateway_resource.web_proxy_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "defect_reporting_post", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "CUSTOM", "authorization_scopes": [], "authorizer_id": "47cejl", "http_method": "POST", "id": "agm-zkvfa0mljb-azeebm-POST", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.header.blink-auth-proxysecret": true, "method.request.header.blink-auth-userid": true}, "request_validator_id": "", "resource_id": "azeebm", "rest_api_id": "zkvfa0mljb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_resource.defect_reporting_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "defects_get", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "CUSTOM", "authorization_scopes": [], "authorizer_id": "47cejl", "http_method": "GET", "id": "agm-zkvfa0mljb-xset15-GET", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.header.blink-auth-proxysecret": true, "method.request.header.blink-auth-userid": true}, "request_validator_id": "", "resource_id": "xset15", "rest_api_id": "zkvfa0mljb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_resource.defects_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "defects_post", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "CUSTOM", "authorization_scopes": [], "authorizer_id": "47cejl", "http_method": "POST", "id": "agm-zkvfa0mljb-xset15-POST", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.header.blink-auth-proxysecret": true, "method.request.header.blink-auth-userid": true}, "request_validator_id": "", "resource_id": "xset15", "rest_api_id": "zkvfa0mljb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_resource.defects_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "root_any", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "CUSTOM", "authorization_scopes": [], "authorizer_id": "47cejl", "http_method": "ANY", "id": "agm-zkvfa0mljb-p7bigfhonl-ANY", "operation_name": "", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "p7bigfhonl", "rest_api_id": "zkvfa0mljb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "web_proxy_any", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "CUSTOM", "authorization_scopes": [], "authorizer_id": "47cejl", "http_method": "ANY", "id": "agm-zkvfa0mljb-omro0b-ANY", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.path.proxy": true}, "request_validator_id": "", "resource_id": "omro0b", "rest_api_id": "zkvfa0mljb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_authorizer.lambda_authorizer", "aws_api_gateway_resource.web_proxy_resource", "aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "defect_reporting_resource", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "azeebm", "parent_id": "p7bigfhonl", "path": "/defect-reporting", "path_part": "defect-reporting", "rest_api_id": "zkvfa0mljb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.bpa_defect_reporting_api"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "defects_resource", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "xset15", "parent_id": "p7bigfhonl", "path": "/defects", "path_part": "defects", "rest_api_id": "zkvfa0mljb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.bpa_defect_reporting_api"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "web_proxy_resource", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "omro0b", "parent_id": "p7bigfhonl", "path": "/{proxy+}", "path_part": "{proxy+}", "rest_api_id": "zkvfa0mljb"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.bpa_defect_reporting_api"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_rest_api", "name": "bpa_defect_reporting_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_source": "HEADER", "arn": "arn:aws:apigateway:eu-central-1::/restapis/zkvfa0mljb", "binary_media_types": [], "body": null, "created_date": "2025-07-18T09:04:22Z", "description": "BPA Defect Reporting API with Lambda Authorizer", "disable_execute_api_endpoint": false, "endpoint_configuration": [{"ip_address_type": "ipv4", "types": ["REGIONAL"], "vpc_endpoint_ids": []}], "execution_arn": "arn:aws:execute-api:eu-central-1:************:zkvfa0mljb", "fail_on_warnings": null, "id": "zkvfa0mljb", "minimum_compression_size": "", "name": "bpa-defect-reporting-gabriel-auth-gabo-dev", "parameters": null, "policy": "", "put_rest_api_mode": null, "root_resource_id": "p7bigfhonl", "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "api_handler_lambda_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:eu-central-1:************:log-group:/aws/lambda/bpa-defect-reporting-api-handler-gabo-gabo-dev", "id": "/aws/lambda/bpa-defect-reporting-api-handler-gabo-gabo-dev", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/lambda/bpa-defect-reporting-api-handler-gabo-gabo-dev", "name_prefix": "", "retention_in_days": 14, "skip_destroy": false, "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.api_handler_lambda_role", "aws_lambda_function.api_handler_lambda", "data.archive_file.api_handler_lambda_zip"]}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "authorizer_lambda_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:eu-central-1:************:log-group:/aws/lambda/bpa-authorizer-lambda-gabo-dev", "id": "/aws/lambda/bpa-authorizer-lambda-gabo-dev", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/lambda/bpa-authorizer-lambda-gabo-dev", "name_prefix": "", "retention_in_days": 14, "skip_destroy": false, "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"]}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "web_proxy_lambda_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:eu-central-1:************:log-group:/aws/lambda/bpa-web-proxy-lambda-gabo-dev", "id": "/aws/lambda/bpa-web-proxy-lambda-gabo-dev", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/lambda/bpa-web-proxy-lambda-gabo-dev", "name_prefix": "", "retention_in_days": 14, "skip_destroy": false, "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.web_proxy_lambda_role", "aws_lambda_function.web_proxy_lambda", "data.archive_file.web_proxy_lambda_zip"]}]}, {"mode": "managed", "type": "aws_dynamodb_table", "name": "defect_cart_table", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:dynamodb:eu-central-1:************:table/bpa-defect-cart-gabo-dev", "attribute": [{"name": "date", "type": "S"}, {"name": "fleetNumber", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [], "hash_key": "fleetNumber", "id": "bpa-defect-cart-gabo-dev", "import_table": [], "local_secondary_index": [], "name": "bpa-defect-cart-gabo-dev", "on_demand_throughput": [], "point_in_time_recovery": [{"enabled": false, "recovery_period_in_days": 0}], "range_key": "date", "read_capacity": 0, "replica": [], "restore_date_time": null, "restore_source_name": null, "restore_source_table_arn": null, "restore_to_latest_time": null, "server_side_encryption": [], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "timeouts": null, "ttl": [{"attribute_name": "", "enabled": false}], "write_capacity": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "api_gateway_authorizer_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bpa-api-gateway-authorizer-role-gabo-dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"apigateway.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-18T09:04:22Z", "description": "", "force_detach_policies": false, "id": "bpa-api-gateway-authorizer-role-gabo-dev", "inline_policy": [{"name": "bpa-api-gateway-authorizer-policy-gabo-dev", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"lambda:InvokeFunction\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:lambda:eu-central-1:************:function:bpa-authorizer-lambda-gabo-dev\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "bpa-api-gateway-authorizer-role-gabo-dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "unique_id": "AROAVRBZJFBMEMTQWUA7V"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role", "name": "api_gateway_web_proxy_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bpa-api-gateway-web-proxy-role-gabo-dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"apigateway.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-23T05:53:30Z", "description": "", "force_detach_policies": false, "id": "bpa-api-gateway-web-proxy-role-gabo-dev", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "bpa-api-gateway-web-proxy-role-gabo-dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "unique_id": "AROAVRBZJFBMBGHHU3KQJ"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "api_handler_lambda_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bpa-api-handler-lambda-role-gabo-dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-18T09:04:22Z", "description": "", "force_detach_policies": false, "id": "bpa-api-handler-lambda-role-gabo-dev", "inline_policy": [{"name": "bpa-api-handler-lambda-policy-gabo-dev", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"dynamodb:PutItem\",\"dynamodb:GetItem\",\"dynamodb:UpdateItem\",\"dynamodb:DeleteItem\",\"dynamodb:Query\",\"dynamodb:Scan\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:eu-central-1:************:table/bpa-defect-cart-gabo-dev\",\"arn:aws:dynamodb:eu-central-1:************:table/bpa-defect-cart-gabo-dev/index/*\"]},{\"Action\":[\"s3:GetObject\",\"s3:PutObject\",\"s3:DeleteObject\",\"s3:GetObjectVersion\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::bpa-defect-images-gabo-dev/*\"]},{\"Action\":[\"s3:ListBucket\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::bpa-defect-images-gabo-dev\"]}]}"}], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"], "max_session_duration": 3600, "name": "bpa-api-handler-lambda-role-gabo-dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "unique_id": "AROAVRBZJFBMAN3X5AF2N"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role", "name": "authorizer_lambda_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bpa-authorizer-lambda-role-gabo-dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-18T09:04:22Z", "description": "", "force_detach_policies": false, "id": "bpa-authorizer-lambda-role-gabo-dev", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"], "max_session_duration": 3600, "name": "bpa-authorizer-lambda-role-gabo-dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "unique_id": "AROAVRBZJFBMGE6YNBNFO"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role", "name": "web_proxy_lambda_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/bpa-web-proxy-lambda-role-gabo-dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-22T14:25:16Z", "description": "", "force_detach_policies": false, "id": "bpa-web-proxy-lambda-role-gabo-dev", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"], "max_session_duration": 3600, "name": "bpa-web-proxy-lambda-role-gabo-dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "unique_id": "AROAVRBZJFBMLV5TWRSGG"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "api_gateway_authorizer_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bpa-api-gateway-authorizer-role-gabo-dev:bpa-api-gateway-authorizer-policy-gabo-dev", "name": "bpa-api-gateway-authorizer-policy-gabo-dev", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"lambda:InvokeFunction\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:lambda:eu-central-1:************:function:bpa-authorizer-lambda-gabo-dev\"}]}", "role": "bpa-api-gateway-authorizer-role-gabo-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.api_gateway_authorizer_role", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "api_gateway_web_proxy_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bpa-api-gateway-web-proxy-role-gabo-dev:bpa-api-gateway-web-proxy-policy-gabo-dev", "name": "bpa-api-gateway-web-proxy-policy-gabo-dev", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"lambda:InvokeFunction\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:lambda:eu-central-1:************:function:bpa-web-proxy-lambda-gabo-dev\"}]}", "role": "bpa-api-gateway-web-proxy-role-gabo-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.api_gateway_web_proxy_role", "aws_iam_role.web_proxy_lambda_role", "aws_lambda_function.web_proxy_lambda", "data.archive_file.web_proxy_lambda_zip"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "api_handler_lambda_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bpa-api-handler-lambda-role-gabo-dev:bpa-api-handler-lambda-policy-gabo-dev", "name": "bpa-api-handler-lambda-policy-gabo-dev", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"dynamodb:PutItem\",\"dynamodb:GetItem\",\"dynamodb:UpdateItem\",\"dynamodb:DeleteItem\",\"dynamodb:Query\",\"dynamodb:Scan\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:eu-central-1:************:table/bpa-defect-cart-gabo-dev\",\"arn:aws:dynamodb:eu-central-1:************:table/bpa-defect-cart-gabo-dev/index/*\"]},{\"Action\":[\"s3:GetObject\",\"s3:PutObject\",\"s3:DeleteObject\",\"s3:GetObjectVersion\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::bpa-defect-images-gabo-dev/*\"]},{\"Action\":[\"s3:ListBucket\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::bpa-defect-images-gabo-dev\"]}]}", "role": "bpa-api-handler-lambda-role-gabo-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.api_handler_lambda_role", "data.aws_caller_identity.current"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "api_handler_lambda_basic", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bpa-api-handler-lambda-role-gabo-dev-20250718090423119300000001", "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "role": "bpa-api-handler-lambda-role-gabo-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.api_handler_lambda_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "authorizer_lambda_basic", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bpa-authorizer-lambda-role-gabo-dev-20250718090423146200000002", "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "role": "bpa-authorizer-lambda-role-gabo-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.authorizer_lambda_role"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "web_proxy_lambda_basic", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "bpa-web-proxy-lambda-role-gabo-dev-20250722142517546000000001", "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "role": "bpa-web-proxy-lambda-role-gabo-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.web_proxy_lambda_role"]}]}, {"mode": "managed", "type": "aws_lambda_function", "name": "api_handler_lambda", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:eu-central-1:************:function:bpa-defect-reporting-api-handler-gabo-gabo-dev", "code_sha256": "M5xsqgHlJz5PSJtY85dOjEnzbbut7u1PnEnBrAW2l7U=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"DEFECT_CART_TABLE": "bpa-defect-cart-gabo-dev", "IMAGES_BUCKET": "bpa-defect-images-gabo-dev"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": "./api-handler-lambda.zip", "function_name": "bpa-defect-reporting-api-handler-gabo-gabo-dev", "handler": "index.handler", "id": "bpa-defect-reporting-api-handler-gabo-gabo-dev", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-defect-reporting-api-handler-gabo-gabo-dev/invocations", "kms_key_arn": "", "last_modified": "2025-07-22T13:28:40.000+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/bpa-defect-reporting-api-handler-gabo-gabo-dev", "system_log_level": ""}], "memory_size": 256, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:eu-central-1:************:function:bpa-defect-reporting-api-handler-gabo-gabo-dev:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-defect-reporting-api-handler-gabo-gabo-dev:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/bpa-api-handler-lambda-role-gabo-dev", "runtime": "nodejs22.x", "s3_bucket": null, "s3_key": null, "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "M5xsqgHlJz5PSJtY85dOjEnzbbut7u1PnEnBrAW2l7U=", "source_code_size": 6207, "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "timeout": 30, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_iam_role.api_handler_lambda_role", "data.archive_file.api_handler_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_lambda_function", "name": "authorizer_lambda", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:eu-central-1:************:function:bpa-authorizer-lambda-gabo-dev", "code_sha256": "fHHVErpwa84x+POUbOEjJN7jYUSaI+iMMsk88oFd0aM=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"BlinkProxySecretKey": "mySecretSecret"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": "./authorizer-lambda.zip", "function_name": "bpa-authorizer-lambda-gabo-dev", "handler": "authorizer-lambda.handler", "id": "bpa-authorizer-lambda-gabo-dev", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-authorizer-lambda-gabo-dev/invocations", "kms_key_arn": "", "last_modified": "2025-07-22T13:28:35.000+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/bpa-authorizer-lambda-gabo-dev", "system_log_level": ""}], "memory_size": 128, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:eu-central-1:************:function:bpa-authorizer-lambda-gabo-dev:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-authorizer-lambda-gabo-dev:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/bpa-authorizer-lambda-role-gabo-dev", "runtime": "nodejs22.x", "s3_bucket": null, "s3_key": null, "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "fHHVErpwa84x+POUbOEjJN7jYUSaI+iMMsk88oFd0aM=", "source_code_size": 1255, "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "timeout": 30, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "environment"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "variables"}, {"type": "index", "value": {"value": "BlinkProxySecretKey", "type": "string"}}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_iam_role.authorizer_lambda_role", "data.archive_file.authorizer_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_lambda_function", "name": "web_proxy_lambda", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:eu-central-1:************:function:bpa-web-proxy-lambda-gabo-dev", "code_sha256": "g9q5ZZ1mrt2BQ/r6aocpsQJ+vvglY9TIr0qP4yMT1o0=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": "./web-proxy-lambda.zip", "function_name": "bpa-web-proxy-lambda-gabo-dev", "handler": "index.handler", "id": "bpa-web-proxy-lambda-gabo-dev", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-web-proxy-lambda-gabo-dev/invocations", "kms_key_arn": "", "last_modified": "2025-07-23T05:24:22.000+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/bpa-web-proxy-lambda-gabo-dev", "system_log_level": ""}], "memory_size": 256, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:eu-central-1:************:function:bpa-web-proxy-lambda-gabo-dev:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:bpa-web-proxy-lambda-gabo-dev:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/bpa-web-proxy-lambda-role-gabo-dev", "runtime": "nodejs22.x", "s3_bucket": null, "s3_key": null, "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "g9q5ZZ1mrt2BQ/r6aocpsQJ+vvglY9TIr0qP4yMT1o0=", "source_code_size": 2107, "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "timeout": 30, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_iam_role.web_proxy_lambda_role", "data.archive_file.web_proxy_lambda_zip"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_lambda_permission", "name": "api_handler_lambda_permission", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "bpa-defect-reporting-api-handler-gabo-gabo-dev", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway-gabo-dev", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:eu-central-1:************:zkvfa0mljb/*/*", "statement_id": "AllowExecutionFromAPIGateway-gabo-dev", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.api_handler_lambda_role", "aws_lambda_function.api_handler_lambda", "data.archive_file.api_handler_lambda_zip"]}]}, {"mode": "managed", "type": "aws_lambda_permission", "name": "authorizer_lambda_permission", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "bpa-authorizer-lambda-gabo-dev", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway-gabo-dev", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:eu-central-1:************:zkvfa0mljb/*/*", "statement_id": "AllowExecutionFromAPIGateway-gabo-dev", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.authorizer_lambda_role", "aws_lambda_function.authorizer_lambda", "data.archive_file.authorizer_lambda_zip"]}]}, {"mode": "managed", "type": "aws_lambda_permission", "name": "web_proxy_lambda_permission", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "bpa-web-proxy-lambda-gabo-dev", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway-WebProxy-gabo-dev", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:eu-central-1:************:zkvfa0mljb/*/*", "statement_id": "AllowExecutionFromAPIGateway-WebProxy-gabo-dev", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.bpa_defect_reporting_api", "aws_iam_role.web_proxy_lambda_role", "aws_lambda_function.web_proxy_lambda", "data.archive_file.web_proxy_lambda_zip"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "defect_images_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::bpa-defect-images-gabo-dev", "bucket": "bpa-defect-images-gabo-dev", "bucket_domain_name": "bpa-defect-images-gabo-dev.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "bpa-defect-images-gabo-dev.s3.eu-central-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "843afb31c8cad8050c714aa2a39d30ad47a274b890799ba7393a122b2c756ac8", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z21DNDUVLTQW6Q", "id": "bpa-defect-images-gabo-dev", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "eu-central-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "tags_all": {"Environment": "gabo-dev", "Project": "bpa-defect-reporting"}, "timeouts": null, "versioning": [{"enabled": true, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "defect_images_pab", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "bpa-defect-images-gabo-dev", "id": "bpa-defect-images-gabo-dev", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.defect_images_bucket"]}]}, {"mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "defect_images_encryption", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bpa-defect-images-gabo-dev", "expected_bucket_owner": "", "id": "bpa-defect-images-gabo-dev", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.defect_images_bucket"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "defect_images_versioning", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "bpa-defect-images-gabo-dev", "expected_bucket_owner": "", "id": "bpa-defect-images-gabo-dev", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.defect_images_bucket"]}]}], "check_results": null}