# BPA Defect Reporting System - Architecture Documentation

## System Overview

The BPA (Bus Performance Analytics) Defect Reporting System is a serverless application built on AWS that provides secure, scalable defect reporting capabilities for bus fleet management.

## Architecture Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Client Web    │    │   CloudFront     │    │   API Gateway       │
│   Application   │◄──►│   Distribution   │◄──►│   (REST API)        │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                                           │
                                                           ▼
                                               ┌─────────────────────┐
                                               │  Lambda Authorizer  │
                                               │  (Authentication)   │
                                               └─────────────────────┘
                                                           │
                                                           ▼
                                                  ┌─────────────────────┐
                                                  │  API Handler Lambda │
                                                  │  (Business Logic)   │
                                                  └─────────────────────┘
                                                           │
                                                           ▼
                                                  ┌─────────────────────┐
                                                  │     Blink API       │
                                                  │  (User Management)  │
                                                  └─────────────────────┘
```

## Component Architecture

### 1. Frontend Layer

#### CloudFront Distribution
- **Purpose**: Content delivery and caching
- **URL**: `https://d2h85rpj1ghd7n.cloudfront.net`
- **Features**: Global edge locations, HTTPS termination, static asset caching

#### Web Application
- **Technology**: React/HTML/CSS/JavaScript
- **Location**: `driver-defects-app/build/`
- **Features**: Responsive UI, defect reporting forms, image upload

### 2. API Gateway Layer

#### REST API: `bpa-defect-reporting-gabriel-auth-{env}`
- **Type**: Regional REST API
- **Authentication**: Custom Lambda authorizer
- **CORS**: Enabled for web access

#### Endpoints:

| Route | Methods | Purpose | Integration |
|-------|---------|---------|-------------|
| `/` | ANY | Web app root | HTTP Proxy (CloudFront) |
| `/{proxy+}` | ANY | Web app sub-paths | HTTP Proxy (CloudFront) |
| `/defect-reporting` | POST | Main defect reporting API | Lambda Proxy (API Handler) |

### 3. Authentication Layer

#### Lambda Authorizer: `bpa-authorizer-lambda-{env}`
- **Runtime**: Node.js 22.x
- **Type**: REQUEST authorizer
- **Caching**: Disabled (TTL = 0)
- **Headers**: `blink-auth-proxysecret`, `blink-auth-userid`

**Features:**
- Timing-safe secret comparison
- Input validation and sanitization
- Comprehensive logging
- No identity source dependencies

### 4. Application Layer



#### API Handler Lambda: `bpa-defect-reporting-api-handler-gabo-{env}`
- **Runtime**: Node.js 22.x
- **Purpose**: Business logic for defect reporting operations
- **Features**:
  - User management via Blink API
  - Defect reporting operations
  - Input validation and error handling
  - Structured logging and monitoring

### 5. External Services

#### Blink API Integration
- **Purpose**: User authentication and management
- **Access**: Via BLINK_APPLICATION_API_TOKEN
- **Features**: User profile retrieval and validation

## Security Architecture

### Authentication Flow

```
1. Client → API Gateway (with auth headers)
2. API Gateway → Lambda Authorizer
3. Authorizer validates headers against environment secret
4. On success: Request proceeds to backend Lambda
5. Backend Lambda fetches user details from Blink API
```

### Security Features

- **Custom Authorization**: Lambda-based with no caching
- **Header Validation**: Required headers with format validation
- **Secret Management**: Environment-based configuration

- **Input Sanitization**: All inputs validated and sanitized
- **CORS Configuration**: Proper cross-origin resource sharing
- **Timing Attack Prevention**: Constant-time secret comparison

## Data Flow

### Defect Reporting Flow

```
1. User accesses web application via API Gateway
2. Authentication headers validated by Lambda authorizer
3. User submits defect report via /defect-reporting endpoint
4. API handler validates user permissions via Blink API
5. Defect data processed and validated
6. Response returned with success/error status
```

## Infrastructure as Code

### Terraform Structure

```
infrastructure/
├── main.tf                 # Main Terraform configuration
├── terraform.tfvars.example # Example variables
└── README.md              # Infrastructure documentation
```

### Key Resources

- **Lambda Functions**: 3 functions with proper IAM roles
- **API Gateway**: REST API with custom authorizer
- **DynamoDB**: Single table with GSI support
- **S3**: Bucket with versioning and encryption
- **CloudWatch**: Log groups for all Lambda functions
- **IAM**: Least-privilege roles and policies

## Environment Management

### Environment Variables

| Component | Variable | Purpose |
|-----------|----------|---------|
| Authorizer | `BlinkProxySecretKey` | Authentication secret |
| API Handler | `BLINK_APPLICATION_API_TOKEN` | Blink API access |

### Environment Separation

- **Development**: `dev` environment for testing
- **Staging**: `staging` environment for pre-production
- **Production**: `prod` environment for live system

## Monitoring and Observability

### CloudWatch Logs

- `/aws/lambda/bpa-authorizer-lambda-{env}`
- `/aws/lambda/bpa-defect-reporting-api-handler-gabo-{env}`


### Metrics

- **Lambda**: Duration, errors, invocations
- **API Gateway**: Request count, latency, 4xx/5xx errors
- **DynamoDB**: Read/write capacity, throttling
- **S3**: Request metrics, error rates

### Request Correlation

- **Request IDs**: Unique identifiers for request tracing
- **Structured Logging**: JSON-formatted logs with correlation
- **Performance Tracking**: Execution time monitoring

## Scalability Considerations

### Auto-Scaling

- **Lambda**: Automatic scaling based on demand
- **DynamoDB**: Pay-per-request scaling
- **API Gateway**: Automatic scaling with throttling limits
- **CloudFront**: Global edge locations

### Performance Optimizations

- **Lambda Context Reuse**: Optimized for container reuse
- **Connection Pooling**: HTTP client optimization
- **Caching Strategy**: CloudFront for static assets
- **Compression**: Optimized content delivery

## Disaster Recovery

### Backup Strategy

- **DynamoDB**: Point-in-time recovery enabled
- **S3**: Versioning and cross-region replication
- **Lambda**: Code stored in version control
- **Infrastructure**: Terraform state management

### Recovery Procedures

1. **Infrastructure**: Terraform apply from version control
2. **Data**: DynamoDB point-in-time recovery
3. **Images**: S3 versioning and replication
4. **Code**: Lambda deployment from CI/CD pipeline

## Cost Optimization

### Pay-per-Use Model

- **Lambda**: Pay per invocation and duration
- **DynamoDB**: Pay-per-request pricing
- **S3**: Pay for storage and requests
- **API Gateway**: Pay per API call

### Cost Controls

- **Lambda Timeouts**: Optimized execution times
- **DynamoDB**: Efficient query patterns
- **S3 Lifecycle**: Automated archival policies
- **CloudWatch**: Log retention policies

## Development Workflow

### Local Development

1. **Code Changes**: Local development with proper testing
2. **Infrastructure**: Terraform plan and apply
3. **Testing**: Comprehensive testing in dev environment
4. **Deployment**: Automated deployment via Terraform

### CI/CD Pipeline

1. **Code Commit**: Push to version control
2. **Validation**: Terraform validate and plan
3. **Testing**: Automated testing in staging
4. **Deployment**: Production deployment approval
5. **Monitoring**: Post-deployment verification

## Future Enhancements

### Planned Features

- **Enhanced Monitoring**: Custom CloudWatch dashboards
- **API Versioning**: Support for multiple API versions
- **Caching Layer**: Redis/ElastiCache for performance
- **Advanced Security**: WAF integration
- **Mobile Support**: Mobile-optimized interfaces

### Scalability Improvements

- **Multi-Region**: Cross-region deployment
- **CDN Optimization**: Advanced CloudFront features
- **Database Optimization**: DynamoDB GSI optimization
- **Microservices**: Service decomposition
