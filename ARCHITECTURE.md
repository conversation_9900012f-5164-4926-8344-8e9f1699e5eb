# BPA Defect Reporting System - Architecture Documentation

## System Overview

The BPA (Bus Performance Analytics) Defect Reporting System is a serverless application built on AWS that provides secure, scalable defect reporting capabilities for bus fleet management.

## Architecture Diagram

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Client Web    │    │   CloudFront     │    │   API Gateway       │
│   Application   │◄──►│   Distribution   │◄──►│   (REST API)        │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                                           │
                                                           ▼
                                               ┌─────────────────────┐
                                               │  Lambda Authorizer  │
                                               │  (Authentication)   │
                                               └─────────────────────┘
                                                           │
                                                           ▼
                                                  ┌─────────────────────┐
                                                  │  API Handler Lambda │
                                                  │  (Business Logic)   │
                                                  └─────────────────────┘
                                                           │
                                                           ▼
                                                  ┌─────────────────────┐
                                                  │     Blink API       │
                                                  │  (User Management)  │
                                                  └─────────────────────┘
```

## Component Architecture

### 1. Frontend Layer

#### CloudFront Distribution
- **Purpose**: Content delivery and caching
- **URL**: `https://d2h85rpj1ghd7n.cloudfront.net`
- **Features**: Global edge locations, HTTPS termination, static asset caching

#### Web Application
- **Technology**: React/HTML/CSS/JavaScript
- **Location**: `driver-defects-app/build/`
- **Features**: Responsive UI, defect reporting forms, image upload

### 2. API Gateway Layer

#### REST API: `bpa-defect-reporting-gabriel-auth-{env}`
- **Type**: Regional REST API
- **Authentication**: Custom Lambda authorizer
- **CORS**: Enabled for web access

#### Endpoints:

| Route | Methods | Purpose | Integration |
|-------|---------|---------|-------------|
| `/` | ANY | Web app root | HTTP Proxy (CloudFront) |
| `/{proxy+}` | ANY | Web app sub-paths | HTTP Proxy (CloudFront) |
| `/defect-reporting` | POST | Main defect reporting API | Lambda Proxy (API Handler) |

### 3. Authentication Layer

#### Lambda Authorizer: `bpa-authorizer-lambda-{env}`
- **Runtime**: Node.js 22.x
- **Type**: REQUEST authorizer
- **Caching**: Disabled (TTL = 0)
- **Headers**: `blink-auth-proxysecret`, `blink-auth-userid`

**Features:**
- Timing-safe secret comparison
- Input validation and sanitization
- Comprehensive logging
- No identity source dependencies

### 4. Application Layer



#### API Handler Lambda: `bpa-defect-reporting-api-handler-gabo-{env}`
- **Runtime**: Node.js 22.x
- **Purpose**: Business logic for defect reporting operations
- **Features**:
  - User management via Blink API
  - Defect reporting operations
  - Input validation and error handling
  - Structured logging and monitoring

### 5. External Services

#### Blink API Integration
- **Purpose**: User authentication and management
- **Access**: Via BLINK_APPLICATION_API_TOKEN
- **Features**: User profile retrieval and validation

## Security Architecture

### Authentication Flow

```
1. Client → API Gateway (with auth headers)
2. API Gateway → Lambda Authorizer
3. Authorizer validates headers against environment secret
4. On success: Request proceeds to backend Lambda
5. Backend Lambda fetches user details from Blink API
```

### Security Features

- **Custom Authorization**: Lambda-based with no caching
- **Header Validation**: Required headers with format validation
- **Secret Management**: Environment-based configuration

- **Input Sanitization**: All inputs validated and sanitized
- **CORS Configuration**: Proper cross-origin resource sharing
- **Timing Attack Prevention**: Constant-time secret comparison

## Data Flow

### Defect Reporting Flow

```
1. User accesses web application via API Gateway
2. Authentication headers validated by Lambda authorizer
3. User submits defect report via /defect-reporting endpoint
4. API handler validates user permissions via Blink API
5. Defect data processed and validated
6. Response returned with success/error status
```

## Infrastructure as Code

### Terraform Structure

```
infrastructure/
├── main.tf                 # Main Terraform configuration
├── terraform.tfvars.example # Example variables
└── README.md              # Infrastructure documentation
```

### Key Resources

- **Lambda Functions**: 3 functions with proper IAM roles
- **API Gateway**: REST API with custom authorizer
- **CloudWatch**: Log groups for all Lambda functions
- **IAM**: Least-privilege roles and policies

## Environment Management

### Environment Variables

| Component | Variable | Purpose |
|-----------|----------|---------|
| Authorizer | `BlinkProxySecretKey` | Authentication secret |
| API Handler | `BLINK_APPLICATION_API_TOKEN` | Blink API access |

### Environment Separation

- **Development**: `dev` environment for testing
- **Staging**: `staging` environment for pre-production
- **Production**: `prod` environment for live system

## Monitoring and Observability

### CloudWatch Logs

- `/aws/lambda/bpa-authorizer-lambda-{env}`
- `/aws/lambda/bpa-defect-reporting-api-handler-gabo-{env}`



