{"name": "bpa-web-proxy-lambda", "version": "1.0.0", "description": "AWS Lambda function for proxying web requests to CloudFront with authentication cookies", "type": "module", "main": "index.js", "engines": {"node": ">=22.0.0"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "echo \"<PERSON><PERSON> not configured\"", "package": "zip -r ../web-proxy-lambda.zip . -x .git/\\* *.md"}, "keywords": ["aws", "lambda", "proxy", "cloudfront", "authentication", "bpa"], "author": "BPA Development Team", "license": "UNLICENSED", "dependencies": {}, "devDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/your-org/bpa.git"}, "bugs": {"url": "https://github.com/your-org/bpa/issues"}, "homepage": "https://github.com/your-org/bpa#readme"}