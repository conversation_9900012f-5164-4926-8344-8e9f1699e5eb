# BPA Defect Reporting Infrastructure

This Terraform configuration deploys the BPA Defect Reporting API Gateway with Lambda Authorizer.

## Architecture

- **Lambda Authorizer**: Node.js 22.x function that validates `blink-auth-proxysecret` and `blink-auth-userid` headers
- **API Handler Lambda**: Node.js 22.x function (`bpa-defect-reporting-api-handler-gabo`) that processes defect reporting operations
- **API Gateway**: REST API named `bpa-defect-reporting-gabriel-auth` with custom authorization
- **DynamoDB Table**: `bpa-defect-cart-{environment}` for storing defect card data
- **S3 Bucket**: `bpa-defect-images-{environment}` for storing defect images
- **IAM Roles**: Proper permissions for Lambda execution, DynamoDB access, S3 access, and API Gateway integration

## Prerequisites

1. AWS CLI configured with appropriate credentials
2. Terraform >= 1.0 installed
3. Node.js Lambda authorizer code in `../authentication/authorizer-lambda.js`
4. Node.js API handler code in `../apps/defect-reporting/backend/api-handler/`

## Deployment Steps

1. **Copy and configure variables**:
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   ```
   Edit `terraform.tfvars` with your specific values.

2. **Initialize Terraform**:
   ```bash
   terraform init
   ```

3. **Plan the deployment**:
   ```bash
   terraform plan
   ```

4. **Apply the configuration**:
   ```bash
   terraform apply
   ```

## Configuration Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `aws_region` | AWS region for deployment | `us-east-1` | No |
| `environment` | Environment name (dev/staging/prod) | `dev` | No |
| `blink_proxy_secret_key` | Secret key for Lambda authorizer | - | Yes |

## Outputs

After successful deployment, you'll get:

- `api_gateway_url`: The base URL of your API Gateway
- `authorizer_lambda_function_name`: Name of the deployed Lambda authorizer
- `api_handler_lambda_function_name`: Name of the deployed API handler Lambda
- `api_gateway_id`: ID of the created API Gateway
- `dynamodb_table_name`: Name of the DynamoDB table for defect cards
- `s3_bucket_name`: Name of the S3 bucket for defect images
- `defects_endpoint_url`: Full URL for the defects endpoint
- `defect_reporting_endpoint_url`: Full URL for the defect-reporting endpoint
- `web_root_endpoint_url`: Full URL for the web root endpoint

## Testing the API

Once deployed, you can test the API by making requests to:

### GET Request (for testing):
```bash
curl -X GET \
  "https://{api_gateway_id}.execute-api.{region}.amazonaws.com/{environment}/defects" \
  -H "blink-auth-proxysecret: your-secret-key" \
  -H "blink-auth-userid: test-user-id"
```

### POST Request to /defects (main API functionality):
```bash
curl -X POST \
  "https://{api_gateway_id}.execute-api.{region}.amazonaws.com/{environment}/defects" \
  -H "Content-Type: application/json" \
  -H "blink-auth-proxysecret: your-secret-key" \
  -H "blink-auth-userid: test-user-id" \
  -d '{
    "action": "GetDefectCard",
    "fleetNumber": "12345"
  }'
```

### POST Request to /defect-reporting:
```bash
curl -X POST \
  "https://{api_gateway_id}.execute-api.{region}.amazonaws.com/{environment}/defect-reporting" \
  -H "Content-Type: application/json" \
  -H "blink-auth-proxysecret: your-secret-key" \
  -H "blink-auth-userid: test-user-id" \
  -d '{
    "action": "AddDefectCard",
    "fleetNumber": "12345",
    "dutyNumber": "D001",
    "vehliceHeight": "3.5m",
    "depot": "Main Depot"
  }'
```

### Web Root Endpoint (proxies to CloudFront):
```bash
# Access the web application root (default page) through API Gateway with authorization
curl -X GET \
  "https://{api_gateway_id}.execute-api.{region}.amazonaws.com/{environment}/" \
  -H "blink-auth-proxysecret: your-secret-key" \
  -H "blink-auth-userid: test-user-id"

# Access specific files or sub-paths (any HTTP method supported)
curl -X GET \
  "https://{api_gateway_id}.execute-api.{region}.amazonaws.com/{environment}/dashboard" \
  -H "blink-auth-proxysecret: your-secret-key" \
  -H "blink-auth-userid: test-user-id"

# POST to sub-paths
curl -X POST \
  "https://{api_gateway_id}.execute-api.{region}.amazonaws.com/{environment}/api/data" \
  -H "Content-Type: application/json" \
  -H "blink-auth-proxysecret: your-secret-key" \
  -H "blink-auth-userid: test-user-id" \
  -d '{"key": "value"}'
```

### Available Actions:
- `GenerateImageUploadUrl`: Generate pre-signed URL for image upload
- `GenerateImageDownloadUrl`: Generate pre-signed URL for image download
- `GetDefectCard`: Retrieve defect card data
- `AddDefectCard`: Add new defect card

## API Gateway Structure

The Terraform script creates:

- **API Gateway**: `bpa-defect-reporting-gabriel-auth-{environment}`
- **Lambda Authorizer**: `bpa-lambda-authorizer-{environment}` - Validates headers and returns authorization context
- **API Endpoints**:
  - `/defects` - GET/POST methods (Lambda integration with `bpa-defect-reporting-api-handler-gabo-{environment}`)
  - `/defect-reporting` - POST method (Lambda integration with `bpa-defect-reporting-api-handler-gabo-{environment}`)
  - `/` - ANY method (HTTP proxy to CloudFront root: `https://d2h85rpj1ghd7n.cloudfront.net/`)
  - `/{proxy+}` - ANY method (HTTP proxy with path forwarding to CloudFront: `https://d2h85rpj1ghd7n.cloudfront.net/`)
- **Deployment Stage**: Uses the environment variable as stage name

## Adding More Endpoints

To add more API endpoints, you can extend the Terraform configuration by adding:

1. New `aws_api_gateway_resource` blocks
2. New `aws_api_gateway_method` blocks with `authorizer_id` reference
3. Corresponding integrations and responses

## Security Notes

- The `blink_proxy_secret_key` is marked as sensitive in Terraform
- Lambda logs are retained for 14 days
- API Gateway uses REGIONAL endpoint configuration
- All resources are tagged with Environment and Project for better organization

## Cleanup

To destroy all resources:

```bash
terraform destroy
```
