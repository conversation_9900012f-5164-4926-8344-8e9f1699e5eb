# Terraform configuration for BPA Defect Reporting API Gateway with Lambda Authorizer
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Variables
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "blink_proxy_secret_key" {
  description = "Blink proxy secret key for authorization"
  type        = string
  sensitive   = true
}

# Data source for current AWS account
data "aws_caller_identity" "current" {}

# Create ZIP archive for Lambda authorizer function
data "archive_file" "authorizer_lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../authentication"
  output_path = "${path.module}/authorizer-lambda.zip"
  excludes    = [".git", "*.md"]
}

# Create ZIP archive for API handler Lambda function
data "archive_file" "api_handler_lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../apps/defect-reporting/backend/api-handler"
  output_path = "${path.module}/api-handler-lambda.zip"
  excludes    = ["node_modules", ".git", "*.md"]
}

# IAM role for Lambda function
resource "aws_iam_role" "authorizer_lambda_role" {
  name = "bpa-authorizer-lambda-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# IAM policy attachment for Lambda basic execution
resource "aws_iam_role_policy_attachment" "authorizer_lambda_basic" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
  role       = aws_iam_role.authorizer_lambda_role.name
}

# IAM role for API handler Lambda function
resource "aws_iam_role" "api_handler_lambda_role" {
  name = "bpa-api-handler-lambda-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# IAM policy attachment for API handler Lambda basic execution
resource "aws_iam_role_policy_attachment" "api_handler_lambda_basic" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
  role       = aws_iam_role.api_handler_lambda_role.name
}

# IAM policy for API handler Lambda to access DynamoDB and S3
resource "aws_iam_role_policy" "api_handler_lambda_policy" {
  name = "bpa-api-handler-lambda-policy-${var.environment}"
  role = aws_iam_role.api_handler_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:GetItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/bpa-defect-cart-${var.environment}",
          "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/bpa-defect-cart-${var.environment}/index/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:GetObjectVersion"
        ]
        Resource = [
          "arn:aws:s3:::bpa-defect-images-${var.environment}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::bpa-defect-images-${var.environment}"
        ]
      }
    ]
  })
}

# Lambda function for API Gateway authorizer
resource "aws_lambda_function" "authorizer_lambda" {
  filename         = data.archive_file.authorizer_lambda_zip.output_path
  function_name    = "bpa-authorizer-lambda-${var.environment}"
  role            = aws_iam_role.authorizer_lambda_role.arn
  handler         = "authorizer-lambda.handler"
  runtime         = "nodejs22.x"
  timeout         = 30
  source_code_hash = data.archive_file.authorizer_lambda_zip.output_base64sha256

  environment {
    variables = {
      BlinkProxySecretKey = var.blink_proxy_secret_key
    }
  }

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# CloudWatch Log Group for Lambda function
resource "aws_cloudwatch_log_group" "authorizer_lambda_logs" {
  name              = "/aws/lambda/${aws_lambda_function.authorizer_lambda.function_name}"
  retention_in_days = 14

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# Lambda function for API handler
resource "aws_lambda_function" "api_handler_lambda" {
  filename         = data.archive_file.api_handler_lambda_zip.output_path
  function_name    = "bpa-defect-reporting-api-handler-gabo-${var.environment}"
  role             = aws_iam_role.api_handler_lambda_role.arn
  handler          = "index.handler"
  runtime          = "nodejs22.x"
  timeout          = 30
  memory_size      = 256
  source_code_hash = data.archive_file.api_handler_lambda_zip.output_base64sha256

  environment {
    variables = {
      DEFECT_CART_TABLE = "bpa-defect-cart-${var.environment}"
      IMAGES_BUCKET     = "bpa-defect-images-${var.environment}"
    }
  }

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# CloudWatch Log Group for API handler Lambda function
resource "aws_cloudwatch_log_group" "api_handler_lambda_logs" {
  name              = "/aws/lambda/${aws_lambda_function.api_handler_lambda.function_name}"
  retention_in_days = 14

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# DynamoDB table for defect cards
resource "aws_dynamodb_table" "defect_cart_table" {
  name           = "bpa-defect-cart-${var.environment}"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "fleetNumber"
  range_key      = "date"

  attribute {
    name = "fleetNumber"
    type = "S"
  }

  attribute {
    name = "date"
    type = "S"
  }

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# S3 bucket for defect images
resource "aws_s3_bucket" "defect_images_bucket" {
  bucket = "bpa-defect-images-${var.environment}"

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# S3 bucket versioning
resource "aws_s3_bucket_versioning" "defect_images_versioning" {
  bucket = aws_s3_bucket.defect_images_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 bucket server-side encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "defect_images_encryption" {
  bucket = aws_s3_bucket.defect_images_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 bucket public access block
resource "aws_s3_bucket_public_access_block" "defect_images_pab" {
  bucket = aws_s3_bucket.defect_images_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# API Gateway REST API
resource "aws_api_gateway_rest_api" "bpa_defect_reporting_api" {
  name        = "bpa-defect-reporting-gabriel-auth-${var.environment}"
  description = "BPA Defect Reporting API with Lambda Authorizer"

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# Lambda permission for API Gateway to invoke the authorizer
resource "aws_lambda_permission" "authorizer_lambda_permission" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.environment}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.authorizer_lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.bpa_defect_reporting_api.execution_arn}/*/*"
}

# Lambda permission for API Gateway to invoke the API handler
resource "aws_lambda_permission" "api_handler_lambda_permission" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.environment}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.api_handler_lambda.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.bpa_defect_reporting_api.execution_arn}/*/*"
}

# API Gateway Lambda Authorizer
resource "aws_api_gateway_authorizer" "lambda_authorizer" {
  name                             = "bpa-lambda-authorizer-${var.environment}"
  rest_api_id                     = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  authorizer_uri                  = aws_lambda_function.authorizer_lambda.invoke_arn
  authorizer_credentials          = aws_iam_role.api_gateway_authorizer_role.arn
  type                            = "REQUEST"
  authorizer_result_ttl_in_seconds = 0  # Disable authorization caching
}

# IAM role for API Gateway to invoke Lambda authorizer
resource "aws_iam_role" "api_gateway_authorizer_role" {
  name = "bpa-api-gateway-authorizer-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Environment = var.environment
    Project     = "bpa-defect-reporting"
  }
}

# IAM policy for API Gateway to invoke Lambda
resource "aws_iam_role_policy" "api_gateway_authorizer_policy" {
  name = "bpa-api-gateway-authorizer-policy-${var.environment}"
  role = aws_iam_role.api_gateway_authorizer_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = aws_lambda_function.authorizer_lambda.arn
      }
    ]
  })
}

# API Gateway resource for root proxy (catch-all for sub-paths)
resource "aws_api_gateway_resource" "root_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  parent_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.root_resource_id
  path_part   = "{proxy+}"
}

# API Gateway resource for defects
resource "aws_api_gateway_resource" "defects_resource" {
  rest_api_id = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  parent_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.root_resource_id
  path_part   = "defects"
}

# API Gateway resource for defect-reporting
resource "aws_api_gateway_resource" "defect_reporting_resource" {
  rest_api_id = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  parent_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.root_resource_id
  path_part   = "defect-reporting"
}

# POST method for defects with authorizer (main API handler endpoint)
resource "aws_api_gateway_method" "defects_post" {
  rest_api_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id   = aws_api_gateway_resource.defects_resource.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id

  request_parameters = {
    "method.request.header.blink-auth-proxysecret" = true
    "method.request.header.blink-auth-userid"      = true
  }
}

# Lambda integration for the POST method
resource "aws_api_gateway_integration" "defects_post_integration" {
  rest_api_id             = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id             = aws_api_gateway_resource.defects_resource.id
  http_method             = aws_api_gateway_method.defects_post.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.api_handler_lambda.invoke_arn
}

# GET method for defects with authorizer (for testing)
resource "aws_api_gateway_method" "defects_get" {
  rest_api_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id   = aws_api_gateway_resource.defects_resource.id
  http_method   = "GET"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id

  request_parameters = {
    "method.request.header.blink-auth-proxysecret" = true
    "method.request.header.blink-auth-userid"      = true
  }
}

# Lambda integration for the GET method
resource "aws_api_gateway_integration" "defects_get_integration" {
  rest_api_id             = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id             = aws_api_gateway_resource.defects_resource.id
  http_method             = aws_api_gateway_method.defects_get.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.api_handler_lambda.invoke_arn
}

# POST method for defect-reporting with authorizer
resource "aws_api_gateway_method" "defect_reporting_post" {
  rest_api_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id   = aws_api_gateway_resource.defect_reporting_resource.id
  http_method   = "POST"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id

  request_parameters = {
    "method.request.header.blink-auth-proxysecret" = true
    "method.request.header.blink-auth-userid"      = true
  }
}

# Lambda integration for the defect-reporting POST method
resource "aws_api_gateway_integration" "defect_reporting_post_integration" {
  rest_api_id             = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id             = aws_api_gateway_resource.defect_reporting_resource.id
  http_method             = aws_api_gateway_method.defect_reporting_post.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.api_handler_lambda.invoke_arn
}

# Root proxy route methods with authorizer - ANY method for {proxy+}
resource "aws_api_gateway_method" "root_proxy_any" {
  rest_api_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id   = aws_api_gateway_resource.root_proxy_resource.id
  http_method   = "ANY"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id

  request_parameters = {
    "method.request.header.blink-auth-proxysecret" = true
    "method.request.header.blink-auth-userid"      = true
    "method.request.path.proxy"                    = true
  }
}

# Root route method with authorizer - ANY method for /
resource "aws_api_gateway_method" "root_any" {
  rest_api_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id   = aws_api_gateway_rest_api.bpa_defect_reporting_api.root_resource_id
  http_method   = "ANY"
  authorization = "CUSTOM"
  authorizer_id = aws_api_gateway_authorizer.lambda_authorizer.id

  request_parameters = {
    "method.request.header.blink-auth-proxysecret" = true
    "method.request.header.blink-auth-userid"      = true
  }
}

# HTTP integrations for root routes to CloudFront

# Root integration for /
resource "aws_api_gateway_integration" "root_integration" {
  rest_api_id             = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id             = aws_api_gateway_rest_api.bpa_defect_reporting_api.root_resource_id
  http_method             = aws_api_gateway_method.root_any.http_method
  integration_http_method = "ANY"
  type                    = "HTTP_PROXY"
  uri                     = "https://d2h85rpj1ghd7n.cloudfront.net/"
}

# Root proxy integration for {proxy+}
resource "aws_api_gateway_integration" "root_proxy_integration" {
  rest_api_id             = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  resource_id             = aws_api_gateway_resource.root_proxy_resource.id
  http_method             = aws_api_gateway_method.root_proxy_any.http_method
  integration_http_method = "ANY"
  type                    = "HTTP_PROXY"
  uri                     = "https://d2h85rpj1ghd7n.cloudfront.net/{proxy}"

  request_parameters = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
}

# API Gateway deployment
resource "aws_api_gateway_deployment" "bpa_api_deployment" {
  depends_on = [
    # Defects endpoints
    aws_api_gateway_method.defects_post,
    aws_api_gateway_integration.defects_post_integration,
    aws_api_gateway_method.defects_get,
    aws_api_gateway_integration.defects_get_integration,
    # Defect-reporting endpoint
    aws_api_gateway_method.defect_reporting_post,
    aws_api_gateway_integration.defect_reporting_post_integration,
    # Root endpoint
    aws_api_gateway_method.root_any,
    aws_api_gateway_integration.root_integration,
    # Web proxy endpoint
    aws_api_gateway_method.root_proxy_any,
    aws_api_gateway_integration.root_proxy_integration,  ]

  rest_api_id = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
  stage_name  = var.environment

  lifecycle {
    create_before_destroy = true
  }
}

# Outputs
output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = "https://${aws_api_gateway_rest_api.bpa_defect_reporting_api.id}.execute-api.${var.aws_region}.amazonaws.com/${var.environment}"
}

output "authorizer_lambda_function_name" {
  description = "Name of the Lambda authorizer function"
  value       = aws_lambda_function.authorizer_lambda.function_name
}

output "api_handler_lambda_function_name" {
  description = "Name of the API handler Lambda function"
  value       = aws_lambda_function.api_handler_lambda.function_name
}

output "api_gateway_id" {
  description = "ID of the API Gateway"
  value       = aws_api_gateway_rest_api.bpa_defect_reporting_api.id
}

output "dynamodb_table_name" {
  description = "Name of the DynamoDB table for defect cards"
  value       = aws_dynamodb_table.defect_cart_table.name
}

output "s3_bucket_name" {
  description = "Name of the S3 bucket for defect images"
  value       = aws_s3_bucket.defect_images_bucket.bucket
}

output "defects_endpoint_url" {
  description = "Full URL for the defects endpoint"
  value       = "https://${aws_api_gateway_rest_api.bpa_defect_reporting_api.id}.execute-api.${var.aws_region}.amazonaws.com/${var.environment}/defects"
}

output "defect_reporting_endpoint_url" {
  description = "Full URL for the defect-reporting endpoint"
  value       = "https://${aws_api_gateway_rest_api.bpa_defect_reporting_api.id}.execute-api.${var.aws_region}.amazonaws.com/${var.environment}/defect-reporting"
}

output "web_root_endpoint_url" {
  description = "Full URL for the web root endpoint"
  value       = "https://${aws_api_gateway_rest_api.bpa_defect_reporting_api.id}.execute-api.${var.aws_region}.amazonaws.com/${var.environment}/"
}

output "web_endpoint_url" {
  description = "URL of the web endpoint (now at root)"
  value       = "https://${aws_api_gateway_rest_api.bpa_defect_reporting_api.id}.execute-api.${var.aws_region}.amazonaws.com/${var.environment}"
}