
import { validateBody, validateDefects, validateInspections } from '../utils/PayloadValidator.js';
import { getCurrentDateAsString } from '../utils/DateUtil.js';
import handleGetDefectCard from './GetDefectCard.js';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { PutCommand, DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';

// Create DynamoDB client
const client = new DynamoDBClient({});
const ddbDocClient = DynamoDBDocumentClient.from(client);

export default async function handleAddDefectCard(body) {
  console.log("AddDefectCard: ", body);
  validateBody(body, ['fleetNumber', 'dutyNumber', 'vehliceHeight', 'depot']);
  validateDefects(body);
  validateInspections(body);

  const tableName = process.env.DEFECT_CART_TABLE;
  const fleetNumber = body.fleetNumber;
  const date = getCurrentDateAsString();

  const inspections = sanitizeInspectionsList(body.inspections ?? []);
  const defects = sanitizeDefectsList(body.defects ?? []);

  const { dutyNumber, vehliceHeight, depot } = body;



  const item = { fleetNumber, date, dutyNumber, vehliceHeight, depot, inspections, defects };

  const params = {
    TableName: tableName,
    Item: item,
    ConditionExpression: "attribute_not_exists(fleetNumber) AND attribute_not_exists(#d)",
    ExpressionAttributeNames: {
      "#d": "date", // 'date' is a reserved word in DynamoDB
    },
  };

  try {
    await ddbDocClient.send(new PutCommand(params));

    const result = await handleGetDefectCard(body);
    result.statusCode = 201;
    return result;
  } catch (error) {
    if (error.name === "ConditionalCheckFailedException") {
      return {
        statusCode: 409,
        body: JSON.stringify({ message: "Item already exists" }),
      };
    }

    console.error("DynamoDB error:", error);

    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal server error" }),
    };
  }
};

const sanitizeInspectionsList = (list) => {
  return Array.isArray(list)
    ? list.map(inspect => ({
      driverNumber: inspect.driverNumber,
      driverName: inspect.driverName,
      inspectionDate: inspect.inspectionDate || new Date().toISOString(),
      defects: Array.isArray(inspect.defects) ? inspect.defects : [],
    }))
    : [];
};

const sanitizeDefectsList = (list) => {
  return Array.isArray(list)
    ? list.map(defect => ({
      area: defect.area,
      condition: defect.condition,
      name: defect.name,
      description: defect.description,
      id: defect.id,
      type: defect.type,
      images: Array.isArray(defect.images)
        ? defect.images.map(img => ({
          contentType: img.contentType,
          key: img.key,
        }))
        : [],
    }))
    : [];
};