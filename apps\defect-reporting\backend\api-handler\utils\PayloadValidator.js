export function validateBody(body, requiredFields = []) {
  const missingFields = requiredFields.filter(field => !(field in body));
  if (missingFields.length > 0) {
    throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
  }
};


export function validateDefects(body) {

  const defects = body.defects ?? [];

  if (!Array.isArray(defects)) {
    throw new Error("Validation failed: 'defects' must be an array or null");
  }

  // If defects has items, validate each one
  for (const [index, defect] of defects.entries()) {
    const requiredFields = ['id', 'type', 'name', 'condition', 'area', 'description', 'images'];
    for (const field of requiredFields) {
      if (defect[field] === undefined || defect[field] === null) {
        throw new Error(`Validation failed: Defect at index ${index} is missing required field: ${field}`);
      }
    }

    // Validate 'images' is array with exactly one item
    if (!Array.isArray(defect.images) || defect.images.length !== 1) {
      throw new Error(`Validation failed: Defect at index ${index} must have exactly one image`);
    }

    const image = defect.images[0];
    if (!image.key || !image.contentType) {
      throw new Error(`Validation failed: Defect at index ${index} has invalid image data`);
    }
  }

};

export function validateInspections(body) {
  if (!body.inspections) {
    throw new Error("'inspections' is required");
  }

  if (!Array.isArray(body.inspections)) {
    throw new Error("'inspections' must be an array");
  }

  if (body.inspections.length !== 1) {
    throw new Error("'inspections' must contain exactly one item");
  }

  const [inspection] = body.inspections;

  if (!inspection.driverNumber) {
    throw new Error("Inspection is missing 'driverNumber'");
  }

  if (!inspection.driverName) {
    throw new Error("Inspection is missing 'driverName'");
  }
 
  if (inspection.inspectionDate) {
    const date = new Date(inspection.inspectionDate);
    if (isNaN(date.getTime())) {
      throw new Error("'inspectionDate' is invalid");
    }

    // Must be UTC (ISO 8601 with 'Z')
    if (!inspection.inspectionDate.endsWith('Z')) {
      throw new Error("'inspectionDate' must be in UTC (end with 'Z')");
    }
  }

    if (inspection.defects) {
      if (!Array.isArray(inspection.defects)) {
        throw new Error("'defects' inside inspection must be an array");
      }

      const defects = body.defects ?? [];
      const defectIds = getDefectIds(defects);
      console.log("defectIds:", defectIds);

      for (const defectId of inspection.defects) {
        if (!defectIds.has(defectId)) {
          throw new Error(`Inspection references unknown defect ID: ${defectId}`);
        }
      }
    }
}


function getDefectIds(defects) {
  const ids = new Set();

  if (!Array.isArray(defects)) return ids;

  for (const defect of defects) {
    if (defect && typeof defect.id === 'string') {
      ids.add(defect.id);
    }
  }

  return ids;
}