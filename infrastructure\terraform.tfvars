# Example Terraform variables file
# Copy this file to terraform.tfvars and update the values

# AWS Region where resources will be deployed
aws_region = "eu-central-1"

# Environment name (dev, staging, prod)
environment = "gabo-dev"

# Blink proxy secret key for Lambda authorizer
# This should match the secret expected by your authorization system
blink_proxy_secret_key = "mySecretSecret"

blink_app_api_token = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
