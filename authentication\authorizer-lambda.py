import re
import os


def lambda_handler(event, context):
    
    for key, value in event.items():
        print(f"{key}: {value}")

    headers = event.get('headers')
    
    if headers is None:
        print("No headers present")
        raise Exception('Unauthorized')

    requestProxySecret = headers.get('blink-auth-proxysecret')
    requestUserId = headers.get('blink-auth-userid')

    if requestProxySecret is None:
        print("No blink-auth-proxysecret header present")
        raise Exception('Unauthorized')

    if requestUserId is None:
        print("No blink-auth-userid header present")
        raise Exception('Unauthorized')

    print(f"Request Blink proxy secret: {requestProxySecret}")
    print(f"Request Blink user ID: {requestUserId}")

    '''
    Validate the incoming token and produce the principal user identifier
    associated with the token. This can be accomplished in a number of ways:

    1. Call out to the OAuth provider
    2. Decode a JWT token inline
    3. Lookup in a self-managed DB
    '''

    blinkProxySecret = os.getenv('BlinkProxySecretKey')
    print("Blink proxy secret saved in lambda: " + blinkProxySecret)

    if blinkProxySecret != requestProxySecret:
        print('Wrong secret used')
        raise Exception('Unauthorized')

    print('Allowed')
    response = {
        "isAuthorized": True,
        "context": {
            "stringKey": "value",
            "numberKey": 1,
            "booleanKey": True,
            "arrayKey": ["value1", "value2"],
            "mapKey": {"value1": "value2"}
        }
    }
    return response    
