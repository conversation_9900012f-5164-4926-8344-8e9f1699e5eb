/**
 * Response Helper Utilities for AWS Lambda API Gateway Integration
 * 
 * Provides standardized response formatting for API Gateway Lambda Proxy integration
 */

/**
 * Create a standardized success response
 * @param {*} data - The data to return in the response body
 * @param {number} statusCode - HTTP status code (default: 200)
 * @param {Object} additionalHeaders - Additional headers to include
 * @returns {Object} API Gateway Lambda Proxy integration response
 */
export function createSuccessResponse(data, statusCode = 200, additionalHeaders = {}) {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
      'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
      ...additionalHeaders
    },
    body: JSON.stringify({
      success: true,
      data,
      timestamp: new Date().toISOString()
    })
  };
}

/**
 * Create a standardized error response
 * @param {number} statusCode - HTTP status code
 * @param {string} message - Error message
 * @param {*} details - Additional error details (optional)
 * @param {Object} additionalHeaders - Additional headers to include
 * @returns {Object} API Gateway Lambda Proxy integration response
 */
export function createErrorResponse(statusCode, message, details = null, additionalHeaders = {}) {
  const errorResponse = {
    success: false,
    error: {
      message,
      statusCode,
      timestamp: new Date().toISOString()
    }
  };

  // Only include details if provided (avoid exposing sensitive info in production)
  if (details !== null && details !== undefined) {
    errorResponse.error.details = details;
  }

  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
      'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
      ...additionalHeaders
    },
    body: JSON.stringify(errorResponse)
  };
}

/**
 * Create a response for OPTIONS requests (CORS preflight)
 * @param {Object} additionalHeaders - Additional headers to include
 * @returns {Object} API Gateway Lambda Proxy integration response
 */
export function createOptionsResponse(additionalHeaders = {}) {
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,blink-auth-proxysecret,blink-auth-userid',
      'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
      'Access-Control-Max-Age': '86400',
      ...additionalHeaders
    },
    body: ''
  };
}

/**
 * Validate and sanitize response data
 * @param {*} data - Data to validate
 * @returns {*} Sanitized data
 */
export function sanitizeResponseData(data) {
  if (data === null || data === undefined) {
    return null;
  }

  // Remove sensitive fields that should never be exposed
  const sensitiveFields = ['password', 'secret', 'token', 'key', 'credential'];
  
  if (typeof data === 'object' && !Array.isArray(data)) {
    const sanitized = { ...data };
    
    Object.keys(sanitized).forEach(key => {
      const lowerKey = key.toLowerCase();
      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        delete sanitized[key];
      } else if (typeof sanitized[key] === 'object') {
        sanitized[key] = sanitizeResponseData(sanitized[key]);
      }
    });
    
    return sanitized;
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeResponseData(item));
  }

  return data;
}
