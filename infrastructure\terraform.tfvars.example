# BPA Defect Reporting System - Terraform Variables Example
# Copy this file to terraform.tfvars and update with your specific values

# AWS Configuration
aws_region = "us-east-1"

# Environment Configuration
# Valid values: dev, staging, prod
environment = "dev"

# Authentication Configuration
# Secret key for proxy authentication - CHANGE THIS VALUE
blink_proxy_secret_key = "your-secure-secret-key-here"

# Blink API Configuration
# API token for accessing Blink services - CHANGE THIS VALUE
blink_application_api_token = "your-blink-api-token-here"

# Optional: Custom naming
# If you want to override default resource names
# project_name = "bpa-defect-reporting"

# Optional: Custom tags
# Additional tags to apply to all resources
# additional_tags = {
#   Owner       = "DevOps Team"
#   CostCenter  = "Engineering"
#   Project     = "BPA"
# }

# Optional: Lambda Configuration
# Uncomment and modify if you need custom Lambda settings
# lambda_timeout = 30
# lambda_memory_size = 256



# Optional: API Gateway Configuration
# Uncomment if you need custom API Gateway settings
# api_gateway_stage_name = "api"

# Optional: CloudWatch Configuration
# Uncomment if you need custom CloudWatch settings
# cloudwatch_log_retention_days = 14

# Development/Testing Configuration
# Only use in development environments
# enable_detailed_monitoring = true
