
import { validateBody } from './PayloadValidator.js'; 

import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const s3 = new S3Client({});

export default async function getValidateUser(event) {
  if (!event) return null;
  if (!event.cookies) return null;

  try {
    
    const cookies = event.cookies || [];
    let batValue = null;

    for (const cookie of cookies) {
        if (cookie.startsWith('_BAT=')) {
            batValue = cookie.split('=')[1];
            break;
        }
    }

    console.log("_BAT value:", batValue);

  } catch (e) {
    console.error("Failed to parse request body:", e);
    return {};
  }
};