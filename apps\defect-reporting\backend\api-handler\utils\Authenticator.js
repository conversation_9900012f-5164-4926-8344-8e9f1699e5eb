/**
 * Authentication and User Management Utilities
 *
 * Handles user authentication and retrieval from Blink API
 */

import axios from 'axios';

// Configure axios defaults for better error handling and timeouts
const apiClient = axios.create({
  timeout: 10000, // 10 second timeout
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`Making API request to: ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API request error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API response received: ${response.status} ${response.statusText}`);
    return response;
  },
  (error) => {
    console.error('API response error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });
    return Promise.reject(error);
  }
);

/**
 * Extract user ID from the Lambda event
 * @param {Object} event - API Gateway Lambda event
 * @returns {string|null} User ID from the authorizer context
 */
export function getUserId(event) {
  try {
    const userId = event?.requestContext?.authorizer?.principalId;

    if (!userId) {
      console.warn('No user ID found in authorizer context');
      return null;
    }

    console.log(`Extracted user ID: ${userId}`);
    return userId;
  } catch (error) {
    console.error('Error extracting user ID:', error);
    return null;
  }
}

/**
 * Validate required environment variables for authentication
 * @throws {Error} If required environment variables are missing
 */
function validateAuthEnvironment() {
  const blinkApiToken = process.env.BLINK_APPLICATION_API_TOKEN;

  if (!blinkApiToken) {
    throw new Error('BLINK_APPLICATION_API_TOKEN environment variable is required');
  }

  return { blinkApiToken };
}

/**
 * Get user information from Blink API
 * @param {Object} event - API Gateway Lambda event
 * @returns {Promise<Object>} User information object
 * @throws {Error} If user retrieval fails
 */
export async function getUser(event) {
  try {
    // Validate environment
    const { blinkApiToken } = validateAuthEnvironment();

    // Extract user ID
    const userId = getUserId(event);
    if (!userId) {
      throw new Error('User ID not found in request context');
    }

    // Validate user ID format (basic validation)
    if (typeof userId !== 'string' || userId.trim().length === 0) {
      throw new Error('Invalid user ID format');
    }

    // Construct API URL with proper encoding
    const encodedUserId = encodeURIComponent(userId.trim());
    const userServiceUrl = `https://api.joinblink.com/v2/user?page=1&limit=1&user_id=${encodedUserId}`;

    console.log(`Fetching user data for user ID: ${userId}`);

    // Make API request
    const response = await apiClient.get(userServiceUrl, {
      headers: {
        'Authorization': `Bearer ${blinkApiToken}`
      }
    });

    // Validate response structure
    if (!response.data) {
      throw new Error('Empty response from Blink API');
    }

    // Extract and validate user data
    const userData = response.data;

    // Return standardized user object with null checks
    const userInfo = {
      companyName: userData.company_name || null,
      firstName: userData.first_name || null,
      secondName: userData.second_name || null,
      displayName: userData.display_name || null,
      employeeId: userData.employee_id || null,
      organisationId: userData.organisation_id || null,
      email: userData.email || null,
      userId: userId
    };

    console.log(`User data retrieved successfully for: ${userInfo.displayName || userInfo.email || userId}`);

    return userInfo;

  } catch (error) {
    // Enhanced error handling with different error types
    if (error.response) {
      // API responded with error status
      const status = error.response.status;
      const message = error.response.data?.message || error.response.statusText;

      if (status === 401) {
        throw new Error('Authentication failed: Invalid API token');
      } else if (status === 404) {
        throw new Error('User not found');
      } else if (status === 429) {
        throw new Error('Rate limit exceeded: Too many requests');
      } else {
        throw new Error(`Blink API error (${status}): ${message}`);
      }
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Network error: Unable to reach Blink API');
    } else {
      // Something else happened
      throw new Error(`User retrieval failed: ${error.message}`);
    }
  }
}

/**
 * Validate user permissions for specific actions
 * @param {Object} userInfo - User information object
 * @param {string} action - Action to validate permissions for
 * @returns {boolean} Whether user has permission for the action
 */
export function validateUserPermissions(userInfo, action) {
  if (!userInfo || !userInfo.userId) {
    return false;
  }

  // Basic permission validation - can be extended based on requirements
  switch (action.toLowerCase()) {
    case 'getuser':
      return true; // All authenticated users can get their own info

    case 'getdefectcard':
    case 'adddefectcard':
      return userInfo.organisationId !== null; // Must belong to an organization

    case 'generateimageuploadurl':
    case 'generateimagedownloadurl':
      return userInfo.organisationId !== null; // Must belong to an organization

    default:
      return false;
  }
}