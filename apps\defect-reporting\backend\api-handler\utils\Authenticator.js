import axios from 'axios'

export async function getUserId(event) {
  return event?.requestContext?.authorizer?.principalId;
};

export default async function getUser(event) {
  const userId = await getUserId(event);

  const userServiceUrl = `https://api.joinblink.com/v2/user?page=1&limit=1&user_id=${userId}`;

  const blinkApiToken = process.env.BLINK_APPLICATION_API_TOKEN;
  
  const response = await axios.get(userServiceUrl, {
    headers: {
      'Authorization': `Bearer ${blinkApiToken}`,
      'accept': 'application/json'
    }
  });

  return {
    'companyName': response.data.company_name,
    'firstName': response.data.first_name,
    'secondName': response.data.second_name,
    'displayName': response.data.display_name,
    'employeeId': response.data.employee_id,
    'organisationId': response.data.organisation_id,
    'email': response.data.email
  };
};