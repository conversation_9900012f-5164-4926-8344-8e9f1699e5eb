**Edit a file, create a new file, and clone from Bitbucket in under 2 minutes**

When you're done, you can delete the content in this README and update the file with details for others getting started with your repository.

*We recommend that you open this README in another tab as you perform the tasks below. You can [watch our video](https://youtu.be/0ocf7u76WSo) for a full demo of all the steps in this tutorial. Open the video in a new tab to avoid leaving Bitbucket.*

---

## Edit a file

You’ll start by editing this README file to learn how to edit a file in Bitbucket.

1. Click **Source** on the left side.
2. Click the # BPA (Bus Performance Analytics) - Defect Reporting System

A comprehensive defect reporting system for bus fleet management, built with AWS serverless architecture.

## 🏗️ Architecture Overview

This system provides a secure, scalable platform for bus defect reporting with the following components:

- **Frontend**: React web application served via CloudFront
- **API Gateway**: RESTful API with custom Lambda authorization and HTTP proxy to CloudFront
- **Lambda Functions**: Serverless backend processing (Authorizer + API Handler)
- **Authentication**: Custom authorization with Blink API integration
- **Infrastructure**: Terraform-managed AWS resources

## 📁 Project Structure

```
bpa/
├── README.md                           # This file
├── .gitignore                         # Git ignore patterns
├── apps/                              # Application components
│   └── defect-reporting/              # Defect reporting application
│       └── backend/                   # Backend services
│           └── api-handler/           # Main API Lambda function
├── authentication/                    # Authentication services
│   ├── authorizer-lambda.js          # Custom Lambda authorizer
│   └── package.json                  # Auth dependencies

├── infrastructure/                    # Infrastructure as Code
│   ├── main.tf                       # Terraform configuration
│   ├── terraform.tfvars.example      # Example variables
│   └── README.md                     # Infrastructure documentation
└── driver-defects-app/              # Frontend application
    └── build/                        # Built frontend assets
```

## 🚀 Quick Start

### Prerequisites

- AWS CLI configured with appropriate credentials
- Terraform >= 1.0
- Node.js >= 22.0.0
- Git

### 1. Clone and Setup

```bash
git clone <repository-url>
cd bpa
```

### 2. Configure Infrastructure

```bash
cd infrastructure
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your specific values
```

### 3. Deploy Infrastructure

```bash
terraform init
terraform plan
terraform apply
```

### 4. Access the Application

After deployment, use the API Gateway URL provided in the Terraform outputs.

## 🔧 Components

### API Gateway Endpoints

| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/` | ANY | Web application (HTTP proxy to CloudFront) | Required |
| `/{proxy+}` | ANY | Web application sub-paths | Required |
| `/defect-reporting` | POST | Main defect reporting API | Required |

### Lambda Functions

1. **Authorizer Lambda** (`bpa-authorizer-lambda-{env}`)
   - Custom API Gateway authorizer
   - Validates `blink-auth-proxysecret` and `blink-auth-userid` headers
   - No caching for real-time authorization

2. **API Handler Lambda** (`bpa-defect-reporting-api-handler-gabo-{env}`)
   - Main business logic for defect reporting
   - User management via Blink API
   - Defect data processing and validation



### External Services

- **Blink API**: User authentication and profile management
- **CloudFront**: Static web application hosting and delivery

## 🔐 Security

### Authentication Flow

1. Client sends request with `blink-auth-proxysecret` and `blink-auth-userid` headers
2. API Gateway invokes custom Lambda authorizer
3. Authorizer validates credentials against environment variables
4. On success, request proceeds to backend Lambda
5. Backend Lambda fetches user details from Blink API

### Security Features

- Custom Lambda authorization with no caching
- Input validation and sanitization
- Secure cookie handling with HttpOnly, Secure, SameSite flags
- CORS configuration for web access
- Environment-based configuration
- Sensitive data filtering in logs and responses

## 🛠️ Development

### Local Development Setup

1. **API Handler Development**:
   ```bash
   cd apps/defect-reporting/backend/api-handler
   npm install
   npm test
   ```

2. **Authentication Development**:
   ```bash
   cd authentication
   # No additional setup required (ES modules)
   ```



### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `BlinkProxySecretKey` | Secret for authorization | Yes |
| `BLINK_APPLICATION_API_TOKEN` | Blink API access token | Yes |

### Testing

```bash
# Test API endpoints
curl -X POST \
  "https://{api-gateway-id}.execute-api.{region}.amazonaws.com/{env}/defect-reporting" \
  -H "Content-Type: application/json" \
  -H "blink-auth-proxysecret: your-secret" \
  -H "blink-auth-userid: user-id" \
  -d '{"action": "GetUser"}'
```

## 📊 Monitoring

### CloudWatch Logs

- `/aws/lambda/bpa-authorizer-lambda-{env}`
- `/aws/lambda/bpa-defect-reporting-api-handler-gabo-{env}`

### Metrics

- Lambda execution duration and errors
- API Gateway request counts and latency

## 🔄 CI/CD

### Deployment Process

1. Code changes pushed to repository
2. Terraform validates infrastructure changes
3. Lambda functions packaged automatically
4. Infrastructure deployed via Terraform
5. Application accessible via API Gateway

### Environment Management

- **Development**: `dev` environment for testing
- **Staging**: `staging` environment for pre-production
- **Production**: `prod` environment for live system

## 📝 API Documentation

### Supported Actions

#### GetUser
Retrieves authenticated user information from Blink API.

#### SubmitDefectReport
Submits a new defect report with validation and processing.

See individual component READMEs for detailed API specifications.

## 🤝 Contributing

1. Follow AWS serverless best practices
2. Maintain comprehensive error handling
3. Add appropriate logging for debugging
4. Validate all inputs and sanitize outputs
5. Update documentation for new features
6. Test thoroughly before deployment

## 📄 License

This project is proprietary and confidential. All rights reserved.

## 🆘 Support

For technical support or questions:
- Check CloudWatch logs for error details
- Review Terraform state for infrastructure issues
- Consult component-specific README files
- Contact the development team

## 🔗 Related Documentation

- [Infrastructure Documentation](./infrastructure/README.md)
- [API Handler Documentation](./apps/defect-reporting/backend/api-handler/README.md)
- [AWS Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws/latest/docs) link from the list of files.
3. Click the **Edit** button.
4. Delete the following text: *Delete this line to make a change to the README from Bitbucket.*
5. After making your change, click **Commit** and then **Commit** again in the dialog. The commit page will open and you’ll see the change you just made.
6. Go back to the **Source** page.

---

## Create a file

Next, you’ll add a new file to this repository.

1. Click the **New file** button at the top of the **Source** page.
2. Give the file a filename of **contributors.txt**.
3. Enter your name in the empty file space.
4. Click **Commit** and then **Commit** again in the dialog.
5. Go back to the **Source** page.

Before you move on, go ahead and explore the repository. You've already seen the **Source** page, but check out the **Commits**, **Branches**, and **Settings** pages.

---

## Clone a repository

Use these steps to clone from SourceTree, our client for using the repository command-line free. Cloning allows you to work on your files locally. If you don't yet have SourceTree, [download and install first](https://www.sourcetreeapp.com/). If you prefer to clone from the command line, see [Clone a repository](https://confluence.atlassian.com/x/4whODQ).

1. You’ll see the clone button under the **Source** heading. Click that button.
2. Now click **Check out in SourceTree**. You may need to create a SourceTree account or log in.
3. When you see the **Clone New** dialog in SourceTree, update the destination path and name if you’d like to and then click **Clone**.
4. Open the directory you just created to see your repository’s files.

Now that you're more familiar with your Bitbucket repository, go ahead and add a new file locally. You can [push your change back to Bitbucket with SourceTree](https://confluence.atlassian.com/x/iqyBMg), or you can [add, commit,](https://confluence.atlassian.com/x/8QhODQ) and [push from the command line](https://confluence.atlassian.com/x/NQ0zDQ).