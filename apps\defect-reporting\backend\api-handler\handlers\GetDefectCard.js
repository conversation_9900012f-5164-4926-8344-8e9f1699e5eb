
import { validateBody } from '../utils/PayloadValidator.js'; 
import { getCurrentDateAsString } from '../utils/DateUtil.js'; 

import { DynamoDBClient  } from '@aws-sdk/client-dynamodb';
import { GetCommand, DynamoDBDocumentClient  } from '@aws-sdk/lib-dynamodb';

// Create DynamoDB client
const client = new DynamoDBClient({});
const ddbDocClient = DynamoDBDocumentClient.from(client);

export default async function handleGetDefectCard(body) {
  console.log("GetDefectCard: ", body);
  validateBody(body, ['fleetNumber']);

  const tableName = process.env.DEFECT_CART_TABLE;
  const primaryKey = body.fleetNumber;
  const sortKey = body.date || getCurrentDateAsString();
  
  try {
    const command = new GetCommand({
      TableName: tableName,
      Key: {
        fleetNumber: primaryKey,
        date: sortKey,
      },
    });

    const result = await ddbDocClient.send(command);

    // TODO: order keys
    return {      
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(result.Item || null)
    };
  } catch (err) {
    console.error("DynamoDB error:", err);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Failed to get item",
        error: err.message,
      }),
    };
  }
};