
import { validateBody } from '../utils/PayloadValidator.js'; 

import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { randomUUID } from "crypto";

const s3 = new S3Client({});

export default async function handleGenerateImageUploadUrl(body) {
  console.log("GenerateImageUploadUrl: ", body);
  validateBody(body, ['contentType']);
  const contentType = body.contentType;
  
  const bucket = process.env.S3_BUCKET_FOR_DEFECT_IMAGES;
  const prefix = process.env.S3_PREFIX_FOR_DEFECT_IMAGES;
  console.log("bucket: ", bucket);
  console.log("prefix: ", prefix);
  console.log("awsRegion: ", process.env.AWS_REGION);
  
  const key = `${prefix}/${getDateAsString()}/${randomUUID()}`;
  
  const command = new PutObjectCommand({
    Bucket: bucket,
    Key: key,
    ContentType: contentType,
  });

  const url = await getSignedUrl(s3, command, { expiresIn: 60 }); // 60 seconds
  
  return {
    statusCode: 200,
    body: JSON.stringify({
      uploadUrl: url,
      key,
      //finalUrl: `https://${bucket}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`,
    }),
  };
};

function getDateAsString() {
  const now = new Date();
  const YYYY = now.getUTCFullYear();
  const MM = String(now.getUTCMonth() + 1).padStart(2, "0");
  const DD = String(now.getUTCDate()).padStart(2, "0");
  return `${YYYY}${MM}${DD}`;
}   