
import { validateBody } from '../utils/PayloadValidator.js'; 

import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const s3 = new S3Client({});

export default async function handleGenerateImageUploadUrl(body) {
  console.log("GenerateImageUploadUrl: ", body);
  validateBody(body, ['key']);
  
  const key = body.key;
  
  const bucket = process.env.S3_BUCKET_FOR_DEFECT_IMAGES;
  console.log("bucket: ", bucket);
  
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key,
  });
  
  const downloadUrl = await getSignedUrl(s3, command, { expiresIn: 60 }); // 60 seconds valid
  
  return {
    statusCode: 200,
    body: JSON.stringify({ downloadUrl }),
  };

};